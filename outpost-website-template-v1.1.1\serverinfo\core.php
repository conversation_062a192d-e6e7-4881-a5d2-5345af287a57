<?php

/**
 * Server Information Tool Core Functions
 * <AUTHOR> Agent
 * @version 1.0.0
 */

class ServerInfoTool {
    
    private $config;
    private $cache_dir;
    
    public function __construct() {
        $this->config = include __DIR__ . '/config.php';
        $this->cache_dir = $this->config['cache']['path'];
        $this->ensureCacheDirectory();
    }
    
    /**
     * Get server information with all configured sections
     */
    public function getServerInfo($serverId, $serverData = null) {
        if (!$this->config['enabled']) {
            return null;
        }
        
        $cacheKey = "server_info_{$serverId}";
        
        // Try to get from cache first
        if ($this->config['cache']['enabled']) {
            $cached = $this->getFromCache($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        // Build server information
        $serverInfo = $this->buildServerInfo($serverId, $serverData);
        
        // Cache the result
        if ($this->config['cache']['enabled']) {
            $this->saveToCache($cacheKey, $serverInfo);
        }
        
        return $serverInfo;
    }
    
    /**
     * Build complete server information array
     */
    private function buildServerInfo($serverId, $serverData) {
        $info = [
            'server_id' => $serverId,
            'timestamp' => time(),
            'sections' => []
        ];
        
        // Get server-specific overrides
        $overrides = $this->config['server_overrides'][$serverId] ?? [];
        
        // Process each enabled section
        foreach ($this->config['sections'] as $sectionKey => $sectionConfig) {
            if (!$sectionConfig['enabled']) {
                continue;
            }
            
            // Apply overrides
            if (isset($overrides['sections'][$sectionKey])) {
                $sectionConfig = array_merge_recursive($sectionConfig, $overrides['sections'][$sectionKey]);
            }
            
            $sectionData = $this->buildSection($sectionKey, $sectionConfig, $serverId, $serverData);
            if ($sectionData) {
                $info['sections'][$sectionKey] = $sectionData;
            }
        }
        
        return $info;
    }
    
    /**
     * Build individual section data
     */
    private function buildSection($sectionKey, $sectionConfig, $serverId, $serverData) {
        $section = [
            'title' => $sectionConfig['title'],
            'icon' => $sectionConfig['icon'],
            'order' => $sectionConfig['order'],
            'collapsible' => $sectionConfig['collapsible'],
            'default_open' => $sectionConfig['default_open'],
            'fields' => []
        ];
        
        foreach ($sectionConfig['fields'] as $fieldKey => $fieldConfig) {
            if (!$fieldConfig['enabled']) {
                continue;
            }
            
            $fieldValue = $this->getFieldValue($fieldKey, $serverId, $serverData, $fieldConfig);
            
            if ($fieldValue !== null) {
                $section['fields'][$fieldKey] = [
                    'label' => $fieldConfig['label'],
                    'value' => $fieldValue,
                    'type' => $fieldConfig['type'] ?? 'text'
                ];
            }
        }
        
        return $section;
    }
    
    /**
     * Get value for a specific field
     */
    private function getFieldValue($fieldKey, $serverId, $serverData, $fieldConfig) {
        // Check if there's a custom value set
        if (isset($fieldConfig['value'])) {
            return $fieldConfig['value'];
        }
        
        // Map field keys to server data
        $fieldMappings = [
            'name' => $serverData['name'] ?? 'Unknown',
            'map' => $serverData['map'] ?? 'Unknown',
            'players' => $serverData['players'] ?? 0,
            'max_players' => $serverData['maxPlayers'] ?? 0,
            'queue' => $serverData['queuedPlayers'] ?? 0,
            'ip' => $serverData['ip'] ?? 'Unknown',
            'port' => $serverData['port'] ?? 'Unknown',
            'connect_command' => isset($serverData['ip'], $serverData['port']) ? "connect {$serverData['ip']}:{$serverData['port']}" : '',
            'steam_connect' => isset($serverData['ip'], $serverData['port']) ? "steam://connect/{$serverData['ip']}:{$serverData['port']}" : '',
            'battlemetrics' => $serverData['battlemetrics'] ?? '',
        ];
        
        // Return mapped value or attempt to get from external sources
        if (isset($fieldMappings[$fieldKey])) {
            return $fieldMappings[$fieldKey];
        }
        
        // Try to get from BattleMetrics or other sources
        return $this->getExternalFieldValue($fieldKey, $serverId, $serverData);
    }
    
    /**
     * Get field value from external sources
     */
    private function getExternalFieldValue($fieldKey, $serverId, $serverData) {
        switch ($fieldKey) {
            case 'uptime':
                return $this->getServerUptime($serverId, $serverData);
            case 'last_wipe':
                return $this->getLastWipeDate($serverId, $serverData);
            case 'next_wipe':
                return $this->getNextWipeDate($serverId, $serverData);
            case 'avg_players':
                return $this->getAveragePlayers($serverId);
            case 'peak_players':
                return $this->getPeakPlayers($serverId);
            case 'server_fps':
                return $this->getServerFPS($serverId);
            case 'entity_count':
                return $this->getEntityCount($serverId);
            default:
                return null;
        }
    }
    
    /**
     * Cache management functions
     */
    private function getFromCache($key) {
        if (!$this->config['cache']['enabled']) {
            return null;
        }
        
        $file = $this->cache_dir . md5($key) . '.cache';
        
        if (!file_exists($file)) {
            return null;
        }
        
        $data = file_get_contents($file);
        $cached = unserialize($data);
        
        if ($cached['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        return $cached['data'];
    }
    
    private function saveToCache($key, $data) {
        if (!$this->config['cache']['enabled']) {
            return;
        }
        
        $file = $this->cache_dir . md5($key) . '.cache';
        $cached = [
            'data' => $data,
            'expires' => time() + $this->config['cache']['duration']
        ];
        
        file_put_contents($file, serialize($cached));
    }
    
    private function ensureCacheDirectory() {
        if (!is_dir($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    /**
     * External data fetching functions (to be implemented)
     */
    private function getServerUptime($serverId, $serverData) {
        // Implement uptime calculation
        return 'Unknown';
    }
    
    private function getLastWipeDate($serverId, $serverData) {
        // Implement last wipe date fetching
        return 'Unknown';
    }
    
    private function getNextWipeDate($serverId, $serverData) {
        // Implement next wipe date calculation
        return 'Unknown';
    }
    
    private function getAveragePlayers($serverId) {
        // Implement average players calculation
        return 'Unknown';
    }
    
    private function getPeakPlayers($serverId) {
        // Implement peak players fetching
        return 'Unknown';
    }
    
    private function getServerFPS($serverId) {
        // Implement server FPS fetching
        return 'Unknown';
    }
    
    private function getEntityCount($serverId) {
        // Implement entity count fetching
        return 'Unknown';
    }
    
    /**
     * Utility functions
     */
    public function getConfig() {
        return $this->config;
    }
    
    public function isEnabled() {
        return $this->config['enabled'];
    }
    
    public function getSectionConfig($sectionKey) {
        return $this->config['sections'][$sectionKey] ?? null;
    }
    
    public function cleanupCache() {
        if (!$this->config['cache']['enabled']) {
            return;
        }
        
        $files = glob($this->cache_dir . '*.cache');
        $now = time();
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cached = unserialize($data);
            
            if ($cached['expires'] < $now) {
                unlink($file);
            }
        }
    }
}

// Initialize the server info tool
$serverInfoTool = new ServerInfoTool();
