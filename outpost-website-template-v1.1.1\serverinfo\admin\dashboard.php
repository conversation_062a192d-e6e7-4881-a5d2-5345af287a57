<?php
session_start();

// Check admin authentication
if (!isset($_SESSION['authorized']) || !$_SESSION['authorized']) {
    header('Location: ../../admin.php');
    exit;
}

// Include required files
require_once '../core.php';

$configFilePath = '../config.php';
$config = include $configFilePath;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $success = false;
    $error = '';
    
    try {
        // Update configuration
        $newConfig = $config;
        
        // Update main settings
        if (isset($_POST['enabled'])) {
            $newConfig['enabled'] = $_POST['enabled'] === 'true';
        }
        
        if (isset($_POST['title'])) {
            $newConfig['title'] = sanitize_input($_POST['title']);
        }
        
        if (isset($_POST['description'])) {
            $newConfig['description'] = sanitize_input($_POST['description']);
        }
        
        // Update display settings
        if (isset($_POST['display'])) {
            foreach ($_POST['display'] as $key => $value) {
                if (isset($newConfig['display'][$key])) {
                    switch ($key) {
                        case 'auto_refresh':
                        case 'show_timestamps':
                        case 'compact_mode':
                            $newConfig['display'][$key] = $value === 'true';
                            break;
                        case 'refresh_interval':
                            $newConfig['display'][$key] = (int)$value;
                            break;
                        default:
                            $newConfig['display'][$key] = sanitize_input($value);
                            break;
                    }
                }
            }
        }
        
        // Update section settings
        if (isset($_POST['sections'])) {
            foreach ($_POST['sections'] as $sectionKey => $sectionData) {
                if (isset($newConfig['sections'][$sectionKey])) {
                    // Update section enabled status
                    if (isset($sectionData['enabled'])) {
                        $newConfig['sections'][$sectionKey]['enabled'] = $sectionData['enabled'] === 'true';
                    }
                    
                    // Update section title
                    if (isset($sectionData['title'])) {
                        $newConfig['sections'][$sectionKey]['title'] = sanitize_input($sectionData['title']);
                    }
                    
                    // Update section order
                    if (isset($sectionData['order'])) {
                        $newConfig['sections'][$sectionKey]['order'] = (int)$sectionData['order'];
                    }
                    
                    // Update section collapsible
                    if (isset($sectionData['collapsible'])) {
                        $newConfig['sections'][$sectionKey]['collapsible'] = $sectionData['collapsible'] === 'true';
                    }
                    
                    // Update section default_open
                    if (isset($sectionData['default_open'])) {
                        $newConfig['sections'][$sectionKey]['default_open'] = $sectionData['default_open'] === 'true';
                    }
                    
                    // Update field settings
                    if (isset($sectionData['fields'])) {
                        foreach ($sectionData['fields'] as $fieldKey => $fieldData) {
                            if (isset($newConfig['sections'][$sectionKey]['fields'][$fieldKey])) {
                                if (isset($fieldData['enabled'])) {
                                    $newConfig['sections'][$sectionKey]['fields'][$fieldKey]['enabled'] = $fieldData['enabled'] === 'true';
                                }
                                if (isset($fieldData['label'])) {
                                    $newConfig['sections'][$sectionKey]['fields'][$fieldKey]['label'] = sanitize_input($fieldData['label']);
                                }
                                if (isset($fieldData['value'])) {
                                    $newConfig['sections'][$sectionKey]['fields'][$fieldKey]['value'] = sanitize_input($fieldData['value']);
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Save configuration
        $configContent = "<?php\n\nreturn " . var_export($newConfig, true) . ";\n";
        
        if (file_put_contents($configFilePath, $configContent)) {
            $success = true;
            $config = $newConfig; // Update local config
            
            // Clear cache
            global $serverInfoTool;
            $serverInfoTool->cleanupCache();
        } else {
            $error = 'Failed to save configuration file.';
        }
        
    } catch (Exception $e) {
        $error = 'Error updating configuration: ' . $e->getMessage();
    }
    
    // Set session message
    if ($success) {
        $_SESSION['message'] = ['type' => 'success', 'text' => 'Server Information Tool settings updated successfully!'];
    } else {
        $_SESSION['message'] = ['type' => 'error', 'text' => $error ?: 'Unknown error occurred.'];
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit;
}

function sanitize_input($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Information Tool - Admin Panel</title>
    <link rel="stylesheet" href="../../admin/assets/css/admin.css">
    <style>
        .section-card {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .section-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #444;
        }
        
        .field-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .field-item {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #555;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #4CAF50;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Server Information Tool</h1>
            <div class="admin-nav">
                <a href="../../admin/dashboard.php" class="btn btn-secondary">← Back to Main Admin</a>
            </div>
        </div>

        <?php if (isset($_SESSION['message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['message']['type']; ?>">
                <?php echo htmlspecialchars($_SESSION['message']['text']); ?>
            </div>
            <?php unset($_SESSION['message']); ?>
        <?php endif; ?>

        <form method="post" class="admin-form">
            <!-- Main Settings -->
            <div class="section-card">
                <div class="section-header">
                    <h2>Main Settings</h2>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Enable Server Information Tool:</label>
                    <label class="toggle-switch">
                        <input type="hidden" name="enabled" value="false">
                        <input type="checkbox" name="enabled" value="true" <?php echo $config['enabled'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Tool Title:</label>
                    <input class="form-input" type="text" name="title" value="<?php echo htmlspecialchars($config['title']); ?>">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Description:</label>
                    <input class="form-input" type="text" name="description" value="<?php echo htmlspecialchars($config['description']); ?>">
                </div>
            </div>

            <!-- Display Settings -->
            <div class="section-card">
                <div class="section-header">
                    <h2>Display Settings</h2>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Layout:</label>
                    <select class="form-input" name="display[layout]">
                        <option value="dropdown" <?php echo $config['display']['layout'] === 'dropdown' ? 'selected' : ''; ?>>Dropdown</option>
                        <option value="tabs" <?php echo $config['display']['layout'] === 'tabs' ? 'selected' : ''; ?>>Tabs</option>
                        <option value="accordion" <?php echo $config['display']['layout'] === 'accordion' ? 'selected' : ''; ?>>Accordion</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Theme:</label>
                    <select class="form-input" name="display[theme]">
                        <option value="dark" <?php echo $config['display']['theme'] === 'dark' ? 'selected' : ''; ?>>Dark</option>
                        <option value="light" <?php echo $config['display']['theme'] === 'light' ? 'selected' : ''; ?>>Light</option>
                        <option value="auto" <?php echo $config['display']['theme'] === 'auto' ? 'selected' : ''; ?>>Auto</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Auto Refresh:</label>
                    <label class="toggle-switch">
                        <input type="hidden" name="display[auto_refresh]" value="false">
                        <input type="checkbox" name="display[auto_refresh]" value="true" <?php echo $config['display']['auto_refresh'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Refresh Interval (seconds):</label>
                    <input class="form-input" type="number" name="display[refresh_interval]" value="<?php echo $config['display']['refresh_interval']; ?>" min="10" max="300">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Show Timestamps:</label>
                    <label class="toggle-switch">
                        <input type="hidden" name="display[show_timestamps]" value="false">
                        <input type="checkbox" name="display[show_timestamps]" value="true" <?php echo $config['display']['show_timestamps'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>

            <!-- Section Configuration -->
            <?php foreach ($config['sections'] as $sectionKey => $section): ?>
            <div class="section-card">
                <div class="section-header">
                    <h3><?php echo htmlspecialchars($section['title']); ?> Section</h3>
                    <label class="toggle-switch">
                        <input type="hidden" name="sections[<?php echo $sectionKey; ?>][enabled]" value="false">
                        <input type="checkbox" name="sections[<?php echo $sectionKey; ?>][enabled]" value="true" <?php echo $section['enabled'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Section Title:</label>
                    <input class="form-input" type="text" name="sections[<?php echo $sectionKey; ?>][title]" value="<?php echo htmlspecialchars($section['title']); ?>">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Order:</label>
                    <input class="form-input" type="number" name="sections[<?php echo $sectionKey; ?>][order]" value="<?php echo $section['order']; ?>" min="1" max="20">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Collapsible:</label>
                    <label class="toggle-switch">
                        <input type="hidden" name="sections[<?php echo $sectionKey; ?>][collapsible]" value="false">
                        <input type="checkbox" name="sections[<?php echo $sectionKey; ?>][collapsible]" value="true" <?php echo $section['collapsible'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Open by Default:</label>
                    <label class="toggle-switch">
                        <input type="hidden" name="sections[<?php echo $sectionKey; ?>][default_open]" value="false">
                        <input type="checkbox" name="sections[<?php echo $sectionKey; ?>][default_open]" value="true" <?php echo $section['default_open'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                </div>
                
                <!-- Fields Configuration -->
                <h4>Fields</h4>
                <div class="field-grid">
                    <?php foreach ($section['fields'] as $fieldKey => $field): ?>
                    <div class="field-item">
                        <h5><?php echo htmlspecialchars($field['label']); ?></h5>
                        
                        <div class="form-group">
                            <label class="form-label">Enabled:</label>
                            <label class="toggle-switch">
                                <input type="hidden" name="sections[<?php echo $sectionKey; ?>][fields][<?php echo $fieldKey; ?>][enabled]" value="false">
                                <input type="checkbox" name="sections[<?php echo $sectionKey; ?>][fields][<?php echo $fieldKey; ?>][enabled]" value="true" <?php echo $field['enabled'] ? 'checked' : ''; ?>>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Label:</label>
                            <input class="form-input" type="text" name="sections[<?php echo $sectionKey; ?>][fields][<?php echo $fieldKey; ?>][label]" value="<?php echo htmlspecialchars($field['label']); ?>">
                        </div>
                        
                        <?php if (strpos($fieldKey, 'custom') === 0): ?>
                        <div class="form-group">
                            <label class="form-label">Custom Value:</label>
                            <input class="form-input" type="text" name="sections[<?php echo $sectionKey; ?>][fields][<?php echo $fieldKey; ?>][value]" value="<?php echo htmlspecialchars($field['value'] ?? ''); ?>">
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Save Settings</button>
                <a href="../../admin/dashboard.php" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</body>
</html>
