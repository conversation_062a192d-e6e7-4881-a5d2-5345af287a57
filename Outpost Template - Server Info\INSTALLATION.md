# 🚀 Quick Installation Guide

## What You Need to Do

### 1. Upload Files
Upload these files to your **Outpost template directory** at `/var/www/stonedflorida420.com/public_html/outpost-website-template-v1.1.1/`:

```
📁 server-info-tool/          ← Upload this entire folder
📄 server-info.php           ← Upload this file
```

### 2. File Structure After Upload
Your directory should look like this:
```
/var/www/stonedflorida420.com/public_html/outpost-website-template-v1.1.1/
├── admin/                    ✅ (existing)
├── assets/                   ✅ (existing)
├── templates/                ✅ (existing)
├── steamauth/                ✅ (existing)
├── config.php                ✅ (existing)
├── core.php                  ✅ (existing)
├── index.php                 ✅ (existing)
├── server-info-tool/         🆕 (NEW - upload this)
│   ├── config.php
│   ├── core.php
│   ├── display.php
│   ├── integration.php
│   ├── api.php
│   ├── assets/
│   │   ├── css/serverinfo.css
│   │   └── js/serverinfo.js
│   └── cache/
└── server-info.php           🆕 (NEW - upload this)
```

### 3. Set Permissions
Run these commands on your server:
```bash
chmod 755 server-info-tool/
mkdir -p server-info-tool/cache/
chmod 755 server-info-tool/cache/
chmod 644 server-info-tool/*.php
```

**Note:** The cache directory will also be created automatically when the tool runs for the first time.

### 4. Template Integration
✅ **ALREADY DONE!** Your template files have been updated:
- `templates/head.php` - Updated to include server info assets
- `templates/navigation.php` - Updated to include "Server Info" menu item

### 5. Test Installation
Visit: https://stonedflorida420.com/outpost-website-template-v1.1.1/server-info.php

## What You Get

🎯 **Features:**
- Dynamic server information pages
- Real-time player counts and server status
- Multiple display layouts (dropdown, tabs, accordion)
- Mobile-responsive design
- Auto-refresh functionality
- BattleMetrics integration
- Customizable sections for each server

🎨 **Display Options:**
- Server overview (name, map, players, uptime)
- Connection info (IP, port, connect commands)
- Server statistics and performance
- Custom rules and features
- Economy and shop information

## Configuration (Optional)

After installation, you can customize the tool by editing:
`server-info-tool/config.php`

### Quick Customization Examples:

**Change layout:**
```php
'display' => [
    'layout' => 'tabs', // dropdown, tabs, accordion
],
```

**Add custom server info:**
```php
'server_overrides' => [
    0 => [ // Server ID 0
        'sections' => [
            'features' => [
                'fields' => [
                    'gather_rate' => ['value' => '5x'],
                    'craft_rate' => ['value' => '3x'],
                ]
            ],
        ]
    ]
],
```

## Troubleshooting

**❌ Page not loading?**
- Check file permissions
- Verify files uploaded correctly
- Check PHP error logs

**❌ Styling looks wrong?**
- Clear browser cache
- Check if CSS files are loading

**❌ No server data?**
- Check your main `config.php` has server information
- Verify server data format matches Outpost template

## Support

If you need help:
1. Check the full README.md for detailed documentation
2. Verify all files uploaded correctly
3. Check server error logs for PHP errors
4. Ensure file permissions are set correctly

---

**That's it!** The tool is designed to work immediately after upload with zero configuration required. 🎉
