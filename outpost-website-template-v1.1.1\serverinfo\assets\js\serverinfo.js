/**
 * Server Information Tool JavaScript
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description Interactive functionality for server info tool
 */

class ServerInfoTool {
    constructor(options = {}) {
        this.options = {
            apiUrl: 'serverinfo/api.php',
            autoRefresh: true,
            refreshInterval: 30000, // 30 seconds
            animationDuration: 300,
            ...options
        };
        
        this.refreshTimers = new Map();
        this.isInitialized = false;
        
        this.init();
    }
    
    /**
     * Initialize the server info tool
     */
    init() {
        if (this.isInitialized) return;
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
        
        this.isInitialized = true;
    }
    
    /**
     * Setup event listeners and initial state
     */
    setup() {
        this.setupCollapsibleSections();
        this.setupTabNavigation();
        this.setupAccordionNavigation();
        this.setupCopyButtons();
        this.setupAutoRefresh();
        
        // Add global event listeners
        document.addEventListener('click', this.handleGlobalClick.bind(this));
        window.addEventListener('beforeunload', this.cleanup.bind(this));
        
        console.log('Server Info Tool initialized');
    }
    
    /**
     * Setup collapsible sections for dropdown layout
     */
    setupCollapsibleSections() {
        const headers = document.querySelectorAll('.server-info-section-header');
        
        headers.forEach(header => {
            header.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSection(header);
            });
        });
    }
    
    /**
     * Toggle section visibility
     */
    toggleSection(header) {
        const section = header.closest('.server-info-section');
        const content = section.querySelector('.server-info-section-content');
        const toggle = header.querySelector('.server-info-toggle');
        
        if (!content) return;
        
        const isOpen = content.classList.contains('show');
        
        if (isOpen) {
            // Close section
            content.classList.remove('show');
            section.classList.remove('open');
            this.animateHeight(content, content.scrollHeight, 0);
        } else {
            // Open section
            content.classList.add('show');
            section.classList.add('open');
            this.animateHeight(content, 0, content.scrollHeight);
        }
        
        // Rotate toggle icon
        if (toggle) {
            toggle.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(180deg)';
        }
    }
    
    /**
     * Setup tab navigation
     */
    setupTabNavigation() {
        const tabLinks = document.querySelectorAll('.server-info-tabs-nav a[data-toggle="tab"]');
        
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchTab(link);
            });
        });
    }
    
    /**
     * Switch active tab
     */
    switchTab(clickedLink) {
        const container = clickedLink.closest('.server-info-tabs-container');
        const targetId = clickedLink.getAttribute('href').substring(1);
        const targetPane = document.getElementById(targetId);
        
        if (!targetPane) return;
        
        // Remove active class from all tabs and panes
        container.querySelectorAll('.server-info-tab-item').forEach(item => {
            item.classList.remove('active');
        });
        
        container.querySelectorAll('.server-info-tab-pane').forEach(pane => {
            pane.classList.remove('active', 'show');
        });
        
        // Add active class to clicked tab and target pane
        clickedLink.closest('.server-info-tab-item').classList.add('active');
        targetPane.classList.add('active', 'show');
        
        // Add animation
        targetPane.classList.add('server-info-fade-in');
        setTimeout(() => {
            targetPane.classList.remove('server-info-fade-in');
        }, this.options.animationDuration);
    }
    
    /**
     * Setup accordion navigation
     */
    setupAccordionNavigation() {
        const accordionHeaders = document.querySelectorAll('.server-info-accordion-header');
        
        accordionHeaders.forEach(header => {
            header.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleAccordion(header);
            });
        });
    }
    
    /**
     * Toggle accordion section
     */
    toggleAccordion(header) {
        const item = header.closest('.server-info-accordion-item');
        const content = item.querySelector('.server-info-accordion-content');
        const toggle = header.querySelector('.server-info-accordion-toggle');
        
        if (!content) return;
        
        const isOpen = content.classList.contains('show');
        
        if (isOpen) {
            content.classList.remove('show');
            content.style.maxHeight = '0';
            content.style.padding = '0 20px';
        } else {
            content.classList.add('show');
            content.style.maxHeight = content.scrollHeight + 40 + 'px'; // Add padding
            content.style.padding = '20px';
        }
        
        // Rotate toggle icon
        if (toggle) {
            toggle.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(180deg)';
        }
    }
    
    /**
     * Setup copy to clipboard functionality
     */
    setupCopyButtons() {
        const copyButtons = document.querySelectorAll('.server-info-copy-btn');
        
        copyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.copyToClipboard(button);
            });
        });
    }
    
    /**
     * Copy text to clipboard
     */
    async copyToClipboard(button) {
        const input = button.previousElementSibling;
        if (!input || !input.value) return;
        
        try {
            await navigator.clipboard.writeText(input.value);
            this.showCopyFeedback(button, 'Copied!');
        } catch (err) {
            // Fallback for older browsers
            input.select();
            document.execCommand('copy');
            this.showCopyFeedback(button, 'Copied!');
        }
    }
    
    /**
     * Show copy feedback
     */
    showCopyFeedback(button, message) {
        const originalText = button.textContent;
        button.textContent = message;
        button.style.background = '#28a745';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '';
        }, 2000);
    }
    
    /**
     * Setup auto-refresh functionality
     */
    setupAutoRefresh() {
        if (!this.options.autoRefresh) return;
        
        const containers = document.querySelectorAll('.server-info-container[data-server-id]');
        
        containers.forEach(container => {
            const serverId = container.dataset.serverId;
            this.startAutoRefresh(serverId);
        });
    }
    
    /**
     * Start auto-refresh for a server
     */
    startAutoRefresh(serverId) {
        if (this.refreshTimers.has(serverId)) {
            clearInterval(this.refreshTimers.get(serverId));
        }
        
        const timer = setInterval(() => {
            this.refreshServerInfo(serverId);
        }, this.options.refreshInterval);
        
        this.refreshTimers.set(serverId, timer);
    }
    
    /**
     * Stop auto-refresh for a server
     */
    stopAutoRefresh(serverId) {
        if (this.refreshTimers.has(serverId)) {
            clearInterval(this.refreshTimers.get(serverId));
            this.refreshTimers.delete(serverId);
        }
    }
    
    /**
     * Refresh server information
     */
    async refreshServerInfo(serverId) {
        const container = document.querySelector(`.server-info-container[data-server-id="${serverId}"]`);
        if (!container) return;
        
        try {
            container.classList.add('server-info-loading');
            
            const response = await fetch(`${this.options.apiUrl}?action=get_server_html&server_id=${serverId}`);
            const data = await response.json();
            
            if (data.success) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = data.html;
                const newContainer = tempDiv.querySelector('.server-info-container');
                
                if (newContainer) {
                    // Preserve current state (open/closed sections)
                    this.preserveState(container, newContainer);
                    
                    // Replace content
                    container.innerHTML = newContainer.innerHTML;
                    
                    // Re-setup event listeners for new content
                    this.setupCollapsibleSections();
                    this.setupCopyButtons();
                    
                    // Update timestamp
                    this.updateTimestamp(container);
                    
                    // Add refresh animation
                    container.classList.add('server-info-fade-in');
                    setTimeout(() => {
                        container.classList.remove('server-info-fade-in');
                    }, this.options.animationDuration);
                }
            }
        } catch (error) {
            console.error('Failed to refresh server info:', error);
        } finally {
            container.classList.remove('server-info-loading');
        }
    }
    
    /**
     * Preserve section states during refresh
     */
    preserveState(oldContainer, newContainer) {
        const oldSections = oldContainer.querySelectorAll('.server-info-section');
        const newSections = newContainer.querySelectorAll('.server-info-section');
        
        oldSections.forEach((oldSection, index) => {
            const newSection = newSections[index];
            if (!newSection) return;
            
            const isOpen = oldSection.classList.contains('open');
            const oldContent = oldSection.querySelector('.server-info-section-content');
            const newContent = newSection.querySelector('.server-info-section-content');
            
            if (isOpen && oldContent && newContent) {
                newSection.classList.add('open');
                newContent.classList.add('show');
            }
        });
    }
    
    /**
     * Update timestamp display
     */
    updateTimestamp(container) {
        const timestamp = container.querySelector('.server-info-timestamp');
        if (timestamp) {
            const now = new Date();
            timestamp.textContent = `Last updated: ${now.toLocaleTimeString()}`;
        }
    }
    
    /**
     * Animate height changes
     */
    animateHeight(element, from, to) {
        element.style.height = from + 'px';
        element.style.overflow = 'hidden';
        
        requestAnimationFrame(() => {
            element.style.transition = `height ${this.options.animationDuration}ms ease`;
            element.style.height = to + 'px';
            
            setTimeout(() => {
                element.style.height = '';
                element.style.overflow = '';
                element.style.transition = '';
            }, this.options.animationDuration);
        });
    }
    
    /**
     * Handle global clicks
     */
    handleGlobalClick(e) {
        // Handle copy button clicks
        if (e.target.classList.contains('server-info-copy-btn')) {
            e.preventDefault();
            this.copyToClipboard(e.target);
        }
    }
    
    /**
     * Manual refresh trigger
     */
    async manualRefresh(serverId) {
        await this.refreshServerInfo(serverId);
    }
    
    /**
     * Get server info data via API
     */
    async getServerInfo(serverId) {
        try {
            const response = await fetch(`${this.options.apiUrl}?action=get_server_info&server_id=${serverId}`);
            const data = await response.json();
            return data.success ? data.data : null;
        } catch (error) {
            console.error('Failed to get server info:', error);
            return null;
        }
    }
    
    /**
     * Update configuration
     */
    updateConfig(newOptions) {
        this.options = { ...this.options, ...newOptions };
        
        // Restart auto-refresh with new interval
        if (newOptions.refreshInterval || newOptions.autoRefresh !== undefined) {
            this.refreshTimers.forEach((timer, serverId) => {
                this.stopAutoRefresh(serverId);
                if (this.options.autoRefresh) {
                    this.startAutoRefresh(serverId);
                }
            });
        }
    }
    
    /**
     * Cleanup resources
     */
    cleanup() {
        this.refreshTimers.forEach((timer) => {
            clearInterval(timer);
        });
        this.refreshTimers.clear();
    }
    
    /**
     * Destroy the instance
     */
    destroy() {
        this.cleanup();
        this.isInitialized = false;
    }
}

// Global copy function for backward compatibility
function copyToClipboard(button) {
    if (window.serverInfoTool) {
        window.serverInfoTool.copyToClipboard(button);
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Check if server info containers exist
    if (document.querySelector('.server-info-container')) {
        window.serverInfoTool = new ServerInfoTool();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ServerInfoTool;
}
