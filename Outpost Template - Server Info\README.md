# Outpost Template - Server Info Tool

A versatile and dynamic server information tool designed to integrate seamlessly with your Outpost template, enabling you to create information-rich pages for each of your servers with dropdown sections and customizable settings.

## 📋 Table of Contents

1. [Features](#features)
2. [Requirements](#requirements)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Integration](#integration)
6. [Usage](#usage)
7. [Customization](#customization)
8. [Troubleshooting](#troubleshooting)
9. [Support](#support)

## ✨ Features

### 🎯 **Core Features**
- **Dynamic Information Display**: Real-time server data with customizable sections
- **Multiple Layout Options**: Dropdown, tabs, and accordion layouts
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Auto-Refresh**: Automatic updates of server information
- **BattleMetrics Integration**: Enhanced data from BattleMetrics API
- **Customizable Sections**: Configure what information to display

### 📊 **Information Sections**
- **Server Overview**: Name, map, players, uptime, wipe dates
- **Connection Info**: IP, port, connect commands, Steam links
- **Statistics**: Player averages, peaks, server performance
- **Server Rules**: Customizable rules and guidelines
- **Features**: Gather rates, PvP/PvE status, available features
- **Economy**: Shop links, VIP information, currency details
- **Custom Fields**: Add your own custom information

### 🎨 **Display Options**
- **Dropdown Layout**: Collapsible sections with smooth animations
- **Tabs Layout**: Organized tabs for easy navigation
- **Accordion Layout**: Expandable sections for compact display
- **Compact Mode**: Minimal display for modals and small spaces

## 🔧 Requirements

- **Outpost Template**: v1.1.1 or higher
- **PHP**: 7.4 or higher
- **Web Server**: Apache/Nginx with mod_rewrite
- **File Permissions**: Write access for cache directory
- **Optional**: BattleMetrics API access for enhanced features

## 📦 Installation

### Step 1: Download and Extract
1. Download the Server Info Tool package
2. Extract the contents to get the `Outpost Template - Server Info` folder

### Step 2: Upload Files
1. Upload the entire `server-info-tool` folder to your Outpost template root directory
2. Upload `server-info.php` to your Outpost template root directory
3. Your directory structure should look like:
   ```
   /var/www/stonedflorida420.com/public_html/
   ├── admin/                       # ← Your existing folder
   ├── assets/                      # ← Your existing folder
   ├── templates/                   # ← Your existing folder
   ├── steamauth/                   # ← Your existing folder
   ├── config.php                   # ← Your existing file
   ├── core.php                     # ← Your existing file
   ├── index.php                    # ← Your existing file
   ├── server-info-tool/            # ← NEW FOLDER (add this)
   │   ├── config.php
   │   ├── core.php
   │   ├── display.php
   │   ├── integration.php
   │   ├── api.php
   │   ├── assets/
   │   │   ├── css/serverinfo.css
   │   │   └── js/serverinfo.js
   │   └── cache/
   └── server-info.php              # ← NEW FILE (add this)
   ```

### Step 3: Set Permissions
Set proper permissions for the cache directory:
```bash
chmod 755 server-info-tool/
chmod 755 server-info-tool/cache/
chmod 644 server-info-tool/*.php
```

### Step 4: Verify Installation
1. Navigate to `https://stonedflorida420.com/server-info.php`
2. You should see the server information page
3. If you see errors, check file permissions and PHP requirements

## ⚙️ Configuration

### Basic Configuration

1. **Edit Configuration File**
   Open `server-info-tool/config.php` and modify:
   ```php
   'enabled' => true,
   'title' => 'Your Server Information',
   'description' => 'Detailed information about our servers',
   ```

2. **Configure Display Settings**
   ```php
   'display' => [
       'layout' => 'dropdown', // dropdown, tabs, accordion
       'theme' => 'dark',      // dark, light, auto
       'auto_refresh' => true,
       'refresh_interval' => 30, // seconds
   ],
   ```

3. **Enable/Disable Sections**
   ```php
   'sections' => [
       'overview' => ['enabled' => true],
       'connection' => ['enabled' => true],
       'statistics' => ['enabled' => false], // Disable if not needed
       // ... more sections
   ],
   ```

### Server-Specific Configuration

Configure custom information for each server:
```php
'server_overrides' => [
    0 => [ // Server ID 0
        'sections' => [
            'features' => [
                'fields' => [
                    'gather_rate' => ['value' => '5x'],
                    'craft_rate' => ['value' => '3x'],
                    'pvp_enabled' => ['value' => true],
                ]
            ],
            'custom' => [
                'fields' => [
                    'custom_field_1' => [
                        'enabled' => true, 
                        'label' => 'Wipe Schedule', 
                        'value' => 'Every Thursday 2PM EST'
                    ],
                ]
            ]
        ]
    ]
],
```

### BattleMetrics Integration

1. **Enable BattleMetrics**
   ```php
   'integrations' => [
       'battlemetrics' => [
           'enabled' => true,
           'api_key' => 'your_api_key_here', // Optional
           'cache_duration' => 300,
       ],
   ],
   ```

2. **Add BattleMetrics IDs**
   In your main Outpost `config.php`, ensure your servers have BattleMetrics IDs:
   ```php
   'servers' => [
       [
           'ip' => 'your.server.ip',
           'port' => 28015,
           'battlemetricsId' => 12345678, // Add this
           'battlemetricsLink' => 'https://www.battlemetrics.com/servers/rust/12345678',
       ],
   ],
   ```

## 🔗 Integration

### Method 1: Automatic Integration (Recommended)

**✅ ALREADY DONE FOR YOU!**

The integration has been automatically added to your template files:

1. **`templates/head.php`** - Updated to include server info tool assets
2. **`templates/navigation.php`** - Updated to include "Server Info" navigation link

No additional changes needed - the tool will work immediately after upload!

### Method 2: Manual Integration (Optional)

If you want to add server info to other pages manually, you can use these code snippets:

1. **Add Server Info to Any Page**
   ```php
   <?php if (file_exists('server-info-tool/display.php')): ?>
       <?php require_once 'server-info-tool/display.php'; ?>
       <?php echo renderServerInfo($serverId, $serverData); ?>
   <?php endif; ?>
   ```

2. **Add Server Info Buttons to Modals**
   ```php
   <?php if (file_exists('server-info-tool/integration.php')): ?>
       <?php require_once 'server-info-tool/integration.php'; ?>
       <?php echo addServerInfoButton($serverId); ?>
   <?php endif; ?>
   ```
```

## 🚀 Usage

### Basic Display

1. **Create Server Info Page**
   Create `server-info.php` in your template root:
   ```php
   <?php
   session_start();
   require_once 'steamauth/steamauth.php';
   require_once 'core.php';
   require_once 'server-info-tool/integration.php';

   include 'templates/head.php';
   include 'templates/navigation.php';
   ?>

   <div class="container">
       <h1>Server Information</h1>
       <?php
       foreach($servers as $serverId => $server) {
           echo renderServerInfo($serverId, $server);
       }
       ?>
   </div>

   <?php include 'templates/footer.php'; ?>
   ```

2. **Add to Existing Pages**
   ```php
   <?php
   require_once 'server-info-tool/display.php';
   echo renderServerInfo(0, $serverData); // Display for server ID 0
   ?>
   ```

### Modal Integration

Add enhanced modals to your existing server cards:
```php
<!-- Enhanced Server Modal -->
<div class="modal fade" id="serverinfo<?php echo $serverId; ?>">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5><?php echo $server['name']; ?> - Detailed Information</h5>
            </div>
            <div class="modal-body">
                <?php echo renderServerInfo($serverId, $server, 'accordion'); ?>
            </div>
        </div>
    </div>
</div>
```

### API Usage

Access server information via API:
```javascript
// Get server info
fetch('server-info-tool/api.php?action=get_server_info&server_id=0')
    .then(response => response.json())
    .then(data => {
        console.log('Server Info:', data);
    });

// Refresh server data
fetch('server-info-tool/api.php?action=refresh_cache&server_id=0');
```

## 🎨 Customization

### Custom Styling

1. **Override CSS**
   Create `server-info-tool/assets/css/custom.css`:
   ```css
   .server-info-container {
       border-radius: 15px;
       box-shadow: 0 4px 8px rgba(0,0,0,0.3);
   }
   
   .server-info-section-header {
       background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
   }
   ```

2. **Include Custom CSS**
   Add to your template:
   ```html
   <link rel="stylesheet" href="server-info-tool/assets/css/custom.css">
   ```

### Custom Fields

Add custom information fields:
```php
'custom' => [
    'enabled' => true,
    'fields' => [
        'discord_server' => [
            'enabled' => true,
            'label' => 'Discord Server',
            'value' => 'https://discord.gg/yourserver',
            'type' => 'url'
        ],
        'admin_contact' => [
            'enabled' => true,
            'label' => 'Admin Contact',
            'value' => '<EMAIL>'
        ],
    ]
],
```

### Layout Customization

Force specific layouts for different contexts:
```php
// Dropdown for main pages
echo renderServerInfo($serverId, $server, 'dropdown');

// Tabs for dedicated server pages
echo renderServerInfo($serverId, $server, 'tabs');

// Accordion for modals
echo renderServerInfo($serverId, $server, 'accordion');

// Compact for small spaces
echo renderCompactServerInfo($serverId, $server);
```

## 🔧 Troubleshooting

### Common Issues

**❌ Server Info Not Displaying**
- Check file permissions (755 for directories, 644 for files)
- Verify the tool is enabled in `config.php`
- Ensure integration files are properly included

**❌ CSS/Styling Issues**
- Clear browser cache
- Check if CSS files are loading (inspect network tab)
- Verify file paths are correct

**❌ Data Not Updating**
- Check cache settings in configuration
- Clear cache manually: delete files in `server-info-tool/cache/`
- Verify BattleMetrics integration is working

**❌ JavaScript Errors**
- Check browser console for errors
- Ensure jQuery/Bootstrap are loaded before server info JS
- Verify API endpoints are accessible

### Debug Mode

Enable debug mode for troubleshooting:
```php
// Add to config.php
'debug' => true,
'log_errors' => true,
```

### File Permissions

Correct permissions:
```bash
# Directories
chmod 755 server-info-tool/
chmod 755 server-info-tool/cache/
chmod 755 server-info-tool/assets/
chmod 755 server-info-tool/admin/

# PHP Files
chmod 644 server-info-tool/*.php
chmod 644 server-info-tool/admin/*.php

# Asset Files
chmod 644 server-info-tool/assets/css/*.css
chmod 644 server-info-tool/assets/js/*.js
```

## 📞 Support

### Getting Help

1. **Check Documentation**: Review this README thoroughly
2. **Check Configuration**: Verify all settings in `config.php`
3. **Test Installation**: Use the test page to verify setup
4. **Check Logs**: Look for PHP errors in your server logs

### Reporting Issues

When reporting issues, please include:
- PHP version
- Outpost template version
- Server info tool version
- Error messages (if any)
- Steps to reproduce the issue

### Version Information

- **Current Version**: 1.0.0
- **Compatible With**: Outpost Template v1.1.1+
- **Last Updated**: 2024

---

**Note**: This tool is designed to work alongside your existing Outpost template without modifying core files. All functionality is contained within the `server-info-tool` directory for easy installation and removal.
