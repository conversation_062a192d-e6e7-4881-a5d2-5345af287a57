<?php

/**
 * Server Information Tool Configuration
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description Versatile server information tool for Outpost template
 */

return [

    /*
    |--------------------------------------------------------------------------
    | Server Information Tool Settings
    |--------------------------------------------------------------------------
    |
    | Configure the main settings for the server information tool
    |
    */

    'enabled' => true,
    'version' => '1.0.0',
    'title' => 'Server Information',
    'description' => 'Detailed server information and statistics',

    /*
    |--------------------------------------------------------------------------
    | Display Settings
    |--------------------------------------------------------------------------
    |
    | Configure how the server information is displayed
    |
    */

    'display' => [
        'layout' => 'dropdown', // dropdown, tabs, accordion
        'theme' => 'dark', // dark, light, auto
        'animation' => 'fade', // fade, slide, none
        'auto_refresh' => true,
        'refresh_interval' => 30, // seconds
        'show_timestamps' => true,
        'compact_mode' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Information Sections
    |--------------------------------------------------------------------------
    |
    | Define what information sections to display for each server
    |
    */

    'sections' => [
        'overview' => [
            'enabled' => true,
            'title' => 'Server Overview',
            'icon' => 'server',
            'order' => 1,
            'collapsible' => true,
            'default_open' => true,
            'fields' => [
                'name' => ['enabled' => true, 'label' => 'Server Name'],
                'map' => ['enabled' => true, 'label' => 'Current Map'],
                'players' => ['enabled' => true, 'label' => 'Players Online'],
                'max_players' => ['enabled' => true, 'label' => 'Max Players'],
                'queue' => ['enabled' => true, 'label' => 'Queue'],
                'uptime' => ['enabled' => true, 'label' => 'Uptime'],
                'last_wipe' => ['enabled' => true, 'label' => 'Last Wipe'],
                'next_wipe' => ['enabled' => true, 'label' => 'Next Wipe'],
            ]
        ],

        'connection' => [
            'enabled' => true,
            'title' => 'Connection Info',
            'icon' => 'link',
            'order' => 2,
            'collapsible' => true,
            'default_open' => false,
            'fields' => [
                'ip' => ['enabled' => true, 'label' => 'Server IP'],
                'port' => ['enabled' => true, 'label' => 'Port'],
                'connect_command' => ['enabled' => true, 'label' => 'Connect Command'],
                'steam_connect' => ['enabled' => true, 'label' => 'Steam Connect'],
                'battlemetrics' => ['enabled' => true, 'label' => 'BattleMetrics'],
            ]
        ],

        'statistics' => [
            'enabled' => true,
            'title' => 'Server Statistics',
            'icon' => 'chart',
            'order' => 3,
            'collapsible' => true,
            'default_open' => false,
            'fields' => [
                'avg_players' => ['enabled' => true, 'label' => 'Average Players (24h)'],
                'peak_players' => ['enabled' => true, 'label' => 'Peak Players (24h)'],
                'total_playtime' => ['enabled' => true, 'label' => 'Total Playtime'],
                'unique_players' => ['enabled' => true, 'label' => 'Unique Players'],
                'server_fps' => ['enabled' => true, 'label' => 'Server FPS'],
                'entity_count' => ['enabled' => true, 'label' => 'Entity Count'],
            ]
        ],

        'rules' => [
            'enabled' => true,
            'title' => 'Server Rules',
            'icon' => 'rules',
            'order' => 4,
            'collapsible' => true,
            'default_open' => false,
            'fields' => [
                'rules_text' => ['enabled' => true, 'label' => 'Rules', 'type' => 'textarea'],
                'rules_url' => ['enabled' => true, 'label' => 'Full Rules URL'],
            ]
        ],

        'features' => [
            'enabled' => true,
            'title' => 'Server Features',
            'icon' => 'features',
            'order' => 5,
            'collapsible' => true,
            'default_open' => false,
            'fields' => [
                'gather_rate' => ['enabled' => true, 'label' => 'Gather Rate'],
                'craft_rate' => ['enabled' => true, 'label' => 'Craft Rate'],
                'stack_size' => ['enabled' => true, 'label' => 'Stack Size'],
                'decay_rate' => ['enabled' => true, 'label' => 'Decay Rate'],
                'pvp_enabled' => ['enabled' => true, 'label' => 'PvP Enabled'],
                'pve_enabled' => ['enabled' => true, 'label' => 'PvE Enabled'],
                'kits_enabled' => ['enabled' => true, 'label' => 'Kits Available'],
                'tp_enabled' => ['enabled' => true, 'label' => 'Teleport Enabled'],
            ]
        ],

        'economy' => [
            'enabled' => true,
            'title' => 'Economy & Shop',
            'icon' => 'shop',
            'order' => 6,
            'collapsible' => true,
            'default_open' => false,
            'fields' => [
                'currency' => ['enabled' => true, 'label' => 'Currency'],
                'shop_enabled' => ['enabled' => true, 'label' => 'Shop Available'],
                'shop_url' => ['enabled' => true, 'label' => 'Shop URL'],
                'vip_available' => ['enabled' => true, 'label' => 'VIP Available'],
                'vip_url' => ['enabled' => true, 'label' => 'VIP Store URL'],
            ]
        ],

        'custom' => [
            'enabled' => true,
            'title' => 'Custom Information',
            'icon' => 'custom',
            'order' => 7,
            'collapsible' => true,
            'default_open' => false,
            'fields' => [
                'custom_field_1' => ['enabled' => false, 'label' => 'Custom Field 1'],
                'custom_field_2' => ['enabled' => false, 'label' => 'Custom Field 2'],
                'custom_field_3' => ['enabled' => false, 'label' => 'Custom Field 3'],
                'custom_text' => ['enabled' => false, 'label' => 'Custom Text', 'type' => 'textarea'],
            ]
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Server-Specific Configurations
    |--------------------------------------------------------------------------
    |
    | Override settings for specific servers
    |
    */

    'server_overrides' => [
        // Example: Override settings for server with ID 0
        // 0 => [
        //     'sections' => [
        //         'features' => [
        //             'fields' => [
        //                 'gather_rate' => ['value' => '5x'],
        //                 'craft_rate' => ['value' => '3x'],
        //             ]
        //         ]
        //     ]
        // ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    |
    | Configure integration with external services
    |
    */

    'integrations' => [
        'battlemetrics' => [
            'enabled' => true,
            'api_key' => '', // Optional: For enhanced data
            'cache_duration' => 300, // 5 minutes
        ],
        'steam' => [
            'enabled' => true,
            'api_key' => '', // Optional: For Steam integration
        ],
        'discord' => [
            'enabled' => false,
            'webhook_url' => '',
            'bot_token' => '',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configure caching for better performance
    |
    */

    'cache' => [
        'enabled' => true,
        'duration' => 300, // 5 minutes
        'path' => '../cache/serverinfo/',
        'cleanup_interval' => 3600, // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security and access control
    |
    */

    'security' => [
        'admin_only_edit' => true,
        'rate_limit' => [
            'enabled' => true,
            'requests_per_minute' => 60,
        ],
        'allowed_html_tags' => ['b', 'i', 'u', 'br', 'p', 'a', 'strong', 'em'],
    ],

];
