<?php

/**
 * Server Information Tool Integration
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description Integration functions for Outpost template
 */

// Include required files
require_once __DIR__ . '/core.php';
require_once __DIR__ . '/display.php';

/**
 * Check if server info tool exists and is enabled
 */
function doesServerInfoToolExist() {
    return file_exists(__DIR__ . '/config.php') && isServerInfoToolEnabled();
}

/**
 * Include server info tool assets in head
 */
function includeServerInfoAssets() {
    if (!doesServerInfoToolExist()) {
        return;
    }
    
    $basePath = 'server-info-tool/';
    echo '<link rel="stylesheet" href="' . $basePath . 'assets/css/serverinfo.css">' . "\n";
    echo '<script src="' . $basePath . 'assets/js/serverinfo.js"></script>' . "\n";
}

/**
 * Add server info button to server modal
 */
function addServerInfoButton($serverId) {
    if (!doesServerInfoToolExist()) {
        return '';
    }
    
    return '<button type="button" class="btn btn-info" onclick="showServerInfoModal(' . $serverId . ')">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Server Details
            </button>';
}

/**
 * Enhanced server modal with server info
 */
function renderEnhancedServerModal($serverId, $server) {
    if (!doesServerInfoToolExist()) {
        return '';
    }
    
    $html = '<div class="modal fade modal-rust" id="serverinfo' . $serverId . '" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">' . htmlspecialchars($server['name']) . ' - Detailed Information</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="server-image-modal-container">
                        <img class="server-image-modal img-fluid" src="' . htmlspecialchars($server['image']) . '" alt="' . htmlspecialchars($server['name']) . ' Server Image">
                        <div class="server-image-modal-overlay"></div>
                    </div>
                    <div class="server-modal-details">
                        <div class="server-info-modal-content">
                            ' . renderServerInfo($serverId, $server, 'accordion') . '
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    ' . (!empty($server['battlemetrics']) ? '<a href="' . htmlspecialchars($server['battlemetrics']) . '" target="_blank" class="btn btn-danger">BattleMetrics</a>' : '') . '
                    ' . (!empty($server['store']) ? '<a href="' . htmlspecialchars($server['store']) . '" target="_blank" class="btn btn-success">Store</a>' : '') . '
                </div>
            </div>
        </div>
    </div>';
    
    return $html;
}

/**
 * Add server info JavaScript functions
 */
function addServerInfoJavaScript() {
    if (!doesServerInfoToolExist()) {
        return '';
    }
    
    return '<script>
        function showServerInfoModal(serverId) {
            const modal = document.getElementById("serverinfo" + serverId);
            if (modal) {
                if (typeof bootstrap !== "undefined") {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                } else {
                    // Fallback for older Bootstrap versions
                    $(modal).modal("show");
                }
            }
        }
        
        function refreshServerInfo(serverId) {
            if (window.serverInfoTool) {
                window.serverInfoTool.manualRefresh(serverId);
            }
        }
        
        function copyServerConnect(serverId, ip, port) {
            const connectString = "connect " + ip + ":" + port;
            navigator.clipboard.writeText(connectString).then(function() {
                // Show success feedback
                showNotification("Connect command copied to clipboard!", "success");
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement("textarea");
                textArea.value = connectString;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand("copy");
                document.body.removeChild(textArea);
                showNotification("Connect command copied to clipboard!", "success");
            });
        }
        
        function showNotification(message, type = "info") {
            // Create notification element
            const notification = document.createElement("div");
            notification.className = "server-info-notification server-info-notification-" + type;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === "success" ? "#28a745" : "#007bff"};
                color: white;
                padding: 12px 20px;
                border-radius: 5px;
                z-index: 9999;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            `;
            
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = "slideOutRight 0.3s ease-in";
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        // Add CSS animations for notifications
        if (!document.getElementById("server-info-notification-styles")) {
            const style = document.createElement("style");
            style.id = "server-info-notification-styles";
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    </script>';
}

/**
 * Create standalone server info page content
 */
function createServerInfoPage($serverId = null, $servers = []) {
    if (!doesServerInfoToolExist()) {
        return '<div class="alert alert-warning">Server Information Tool is not available.</div>';
    }
    
    $html = '';
    
    if ($serverId !== null && isset($servers[$serverId])) {
        // Single server page
        $server = $servers[$serverId];
        $html .= '<div class="container">';
        $html .= '<div class="row justify-content-center">';
        $html .= '<div class="col-12 col-lg-10">';
        $html .= '<h1 class="text-center mb-4">' . htmlspecialchars($server['name']) . ' - Server Information</h1>';
        $html .= renderServerInfo($serverId, $server);
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
    } else {
        // All servers page
        $html .= '<div class="container">';
        $html .= '<h1 class="text-center mb-4">Server Information</h1>';
        $html .= '<div class="row">';
        
        foreach ($servers as $id => $server) {
            $html .= '<div class="col-12 col-lg-6 mb-4">';
            $html .= '<div class="server-info-page-card">';
            $html .= '<h3>' . htmlspecialchars($server['name']) . '</h3>';
            $html .= renderServerInfo($id, $server);
            $html .= '</div>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
    }
    
    return $html;
}

/**
 * Add server info widget to dashboard
 */
function addServerInfoWidget($servers = []) {
    if (!doesServerInfoToolExist()) {
        return '';
    }
    
    $html = '<div class="server-info-widget">';
    $html .= '<h4>Server Status Overview</h4>';
    $html .= '<div class="server-info-widget-grid">';
    
    foreach ($servers as $id => $server) {
        $isOnline = $server['online'] ?? false;
        $players = $server['players'] ?? 0;
        $maxPlayers = $server['maxPlayers'] ?? 0;
        
        $html .= '<div class="server-info-widget-item">';
        $html .= '<div class="server-info-widget-header">';
        $html .= '<span class="server-name">' . htmlspecialchars($server['name']) . '</span>';
        $html .= '<span class="server-status ' . ($isOnline ? 'online' : 'offline') . '">';
        $html .= $isOnline ? 'Online' : 'Offline';
        $html .= '</span>';
        $html .= '</div>';
        
        if ($isOnline) {
            $html .= '<div class="server-info-widget-stats">';
            $html .= '<span>Players: ' . $players . '/' . $maxPlayers . '</span>';
            $html .= '<span>Map: ' . htmlspecialchars($server['map'] ?? 'Unknown') . '</span>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Get server info tool configuration for JavaScript
 */
function getServerInfoConfig() {
    if (!doesServerInfoToolExist()) {
        return '{}';
    }
    
    global $serverInfoTool;
    $config = $serverInfoTool->getConfig();
    
    // Remove sensitive data
    unset($config['integrations']);
    unset($config['security']);
    
    return json_encode($config);
}

/**
 * Initialize server info tool integration
 */
function initServerInfoIntegration() {
    if (!doesServerInfoToolExist()) {
        return;
    }
    
    // Add JavaScript configuration
    echo '<script>window.serverInfoConfig = ' . getServerInfoConfig() . ';</script>';
    
    // Add custom JavaScript functions
    echo addServerInfoJavaScript();
}

/**
 * Add navigation item for server info
 */
function addServerInfoNavigation() {
    if (!doesServerInfoToolExist()) {
        return '';
    }
    
    return '<li class="nav-item">
                <a class="nav-link" href="server-info.php">Server Info</a>
            </li>';
}

/**
 * Get server info data as JSON for API
 */
function getServerInfoJSON($serverId, $serverData = null) {
    if (!doesServerInfoToolExist()) {
        return json_encode(['error' => 'Server Info Tool not available']);
    }
    
    $data = getServerInfoData($serverId, $serverData);
    return json_encode($data);
}

// Auto-initialize if not in admin context
if (!defined('ADMIN_CONTEXT')) {
    initServerInfoIntegration();
}
