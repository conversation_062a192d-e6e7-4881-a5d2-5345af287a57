<?php

/**
 * Server Information Display Component
 * <AUTHOR> Agent
 * @version 1.0.0
 */

// Include the core functionality
require_once __DIR__ . '/core.php';

class ServerInfoDisplay {
    
    private $serverInfoTool;
    private $config;
    
    public function __construct() {
        global $serverInfoTool;
        $this->serverInfoTool = $serverInfoTool;
        $this->config = $this->serverInfoTool->getConfig();
    }
    
    /**
     * Render server information for a specific server
     */
    public function renderServerInfo($serverId, $serverData = null, $layout = null) {
        if (!$this->serverInfoTool->isEnabled()) {
            return '';
        }
        
        $serverInfo = $this->serverInfoTool->getServerInfo($serverId, $serverData);
        
        if (!$serverInfo) {
            return '';
        }
        
        $displayLayout = $layout ?: $this->config['display']['layout'];
        
        switch ($displayLayout) {
            case 'dropdown':
                return $this->renderDropdownLayout($serverInfo);
            case 'tabs':
                return $this->renderTabsLayout($serverInfo);
            case 'accordion':
                return $this->renderAccordionLayout($serverInfo);
            default:
                return $this->renderDropdownLayout($serverInfo);
        }
    }
    
    /**
     * Render dropdown layout
     */
    private function renderDropdownLayout($serverInfo) {
        $serverId = $serverInfo['server_id'];
        $sections = $serverInfo['sections'];
        
        // Sort sections by order
        uasort($sections, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        
        $html = '<div class="server-info-container" data-server-id="' . $serverId . '">';
        $html .= '<div class="server-info-header">';
        $html .= '<h3 class="server-info-title">' . $this->config['title'] . '</h3>';
        
        if ($this->config['display']['show_timestamps']) {
            $html .= '<span class="server-info-timestamp">Last updated: ' . date('H:i:s', $serverInfo['timestamp']) . '</span>';
        }
        
        $html .= '</div>';
        
        foreach ($sections as $sectionKey => $section) {
            $html .= $this->renderDropdownSection($sectionKey, $section, $serverId);
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render individual dropdown section
     */
    private function renderDropdownSection($sectionKey, $section, $serverId) {
        $isOpen = $section['default_open'] ? 'open' : '';
        $sectionId = "server-info-{$serverId}-{$sectionKey}";
        
        $html = '<div class="server-info-section ' . $isOpen . '" data-section="' . $sectionKey . '">';
        
        // Section header
        $html .= '<div class="server-info-section-header" data-toggle="collapse" data-target="#' . $sectionId . '">';
        $html .= '<div class="server-info-section-title">';
        $html .= '<i class="server-info-icon icon-' . $section['icon'] . '"></i>';
        $html .= '<span>' . htmlspecialchars($section['title']) . '</span>';
        $html .= '</div>';
        
        if ($section['collapsible']) {
            $html .= '<div class="server-info-toggle">';
            $html .= '<i class="toggle-icon"></i>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        // Section content
        $collapseClass = $section['default_open'] ? 'show' : '';
        $html .= '<div id="' . $sectionId . '" class="server-info-section-content collapse ' . $collapseClass . '">';
        $html .= '<div class="server-info-fields">';
        
        foreach ($section['fields'] as $fieldKey => $field) {
            $html .= $this->renderField($fieldKey, $field);
        }
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render individual field
     */
    private function renderField($fieldKey, $field) {
        $type = $field['type'] ?? 'text';
        $value = $field['value'];
        
        $html = '<div class="server-info-field" data-field="' . $fieldKey . '">';
        $html .= '<div class="server-info-field-label">' . htmlspecialchars($field['label']) . ':</div>';
        $html .= '<div class="server-info-field-value">';
        
        switch ($type) {
            case 'textarea':
                $html .= '<div class="server-info-textarea">' . nl2br(htmlspecialchars($value)) . '</div>';
                break;
            case 'url':
                if (!empty($value)) {
                    $html .= '<a href="' . htmlspecialchars($value) . '" target="_blank" rel="noopener">' . htmlspecialchars($value) . '</a>';
                } else {
                    $html .= '<span class="server-info-empty">Not available</span>';
                }
                break;
            case 'boolean':
                $html .= '<span class="server-info-boolean ' . ($value ? 'enabled' : 'disabled') . '">';
                $html .= $value ? 'Yes' : 'No';
                $html .= '</span>';
                break;
            case 'copy':
                $html .= '<div class="server-info-copy-field">';
                $html .= '<input type="text" class="server-info-copy-input" readonly value="' . htmlspecialchars($value) . '">';
                $html .= '<button class="server-info-copy-btn" onclick="copyToClipboard(this)">Copy</button>';
                $html .= '</div>';
                break;
            default:
                if (empty($value) || $value === 'Unknown') {
                    $html .= '<span class="server-info-empty">Not available</span>';
                } else {
                    $html .= htmlspecialchars($value);
                }
                break;
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render tabs layout
     */
    private function renderTabsLayout($serverInfo) {
        $serverId = $serverInfo['server_id'];
        $sections = $serverInfo['sections'];
        
        // Sort sections by order
        uasort($sections, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        
        $html = '<div class="server-info-tabs-container" data-server-id="' . $serverId . '">';
        
        // Tab headers
        $html .= '<ul class="server-info-tabs-nav">';
        $first = true;
        foreach ($sections as $sectionKey => $section) {
            $activeClass = $first ? 'active' : '';
            $html .= '<li class="server-info-tab-item ' . $activeClass . '">';
            $html .= '<a href="#tab-' . $serverId . '-' . $sectionKey . '" data-toggle="tab">';
            $html .= '<i class="server-info-icon icon-' . $section['icon'] . '"></i>';
            $html .= '<span>' . htmlspecialchars($section['title']) . '</span>';
            $html .= '</a>';
            $html .= '</li>';
            $first = false;
        }
        $html .= '</ul>';
        
        // Tab content
        $html .= '<div class="server-info-tabs-content">';
        $first = true;
        foreach ($sections as $sectionKey => $section) {
            $activeClass = $first ? 'active show' : '';
            $html .= '<div id="tab-' . $serverId . '-' . $sectionKey . '" class="server-info-tab-pane ' . $activeClass . '">';
            
            foreach ($section['fields'] as $fieldKey => $field) {
                $html .= $this->renderField($fieldKey, $field);
            }
            
            $html .= '</div>';
            $first = false;
        }
        $html .= '</div>';
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render accordion layout
     */
    private function renderAccordionLayout($serverInfo) {
        $serverId = $serverInfo['server_id'];
        $sections = $serverInfo['sections'];
        
        // Sort sections by order
        uasort($sections, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });
        
        $html = '<div class="server-info-accordion-container" data-server-id="' . $serverId . '">';
        
        foreach ($sections as $sectionKey => $section) {
            $sectionId = "accordion-{$serverId}-{$sectionKey}";
            $isOpen = $section['default_open'];
            
            $html .= '<div class="server-info-accordion-item">';
            $html .= '<div class="server-info-accordion-header" data-toggle="collapse" data-target="#' . $sectionId . '">';
            $html .= '<i class="server-info-icon icon-' . $section['icon'] . '"></i>';
            $html .= '<span>' . htmlspecialchars($section['title']) . '</span>';
            $html .= '<i class="server-info-accordion-toggle"></i>';
            $html .= '</div>';
            
            $collapseClass = $isOpen ? 'show' : '';
            $html .= '<div id="' . $sectionId . '" class="server-info-accordion-content collapse ' . $collapseClass . '">';
            
            foreach ($section['fields'] as $fieldKey => $field) {
                $html .= $this->renderField($fieldKey, $field);
            }
            
            $html .= '</div>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render compact server info for modal or small spaces
     */
    public function renderCompactInfo($serverId, $serverData = null) {
        $serverInfo = $this->serverInfoTool->getServerInfo($serverId, $serverData);
        
        if (!$serverInfo) {
            return '';
        }
        
        $html = '<div class="server-info-compact">';
        
        // Show only overview section in compact mode
        if (isset($serverInfo['sections']['overview'])) {
            $section = $serverInfo['sections']['overview'];
            
            foreach ($section['fields'] as $fieldKey => $field) {
                if (in_array($fieldKey, ['name', 'map', 'players', 'max_players'])) {
                    $html .= '<div class="server-info-compact-field">';
                    $html .= '<span class="label">' . $field['label'] . ':</span> ';
                    $html .= '<span class="value">' . htmlspecialchars($field['value']) . '</span>';
                    $html .= '</div>';
                }
            }
        }
        
        $html .= '</div>';
        
        return $html;
    }
}

// Initialize the display component
$serverInfoDisplay = new ServerInfoDisplay();

// Helper functions for easy integration
function renderServerInfo($serverId, $serverData = null, $layout = null) {
    global $serverInfoDisplay;
    if (!isset($serverInfoDisplay)) {
        $serverInfoDisplay = new ServerInfoDisplay();
    }
    return $serverInfoDisplay->renderServerInfo($serverId, $serverData, $layout);
}

function renderCompactServerInfo($serverId, $serverData = null) {
    global $serverInfoDisplay;
    if (!isset($serverInfoDisplay)) {
        $serverInfoDisplay = new ServerInfoDisplay();
    }
    return $serverInfoDisplay->renderCompactInfo($serverId, $serverData);
}

function getServerInfoData($serverId, $serverData = null) {
    global $serverInfoTool;
    if (!isset($serverInfoTool)) {
        $serverInfoTool = new ServerInfoTool();
    }
    return $serverInfoTool->getServerInfo($serverId, $serverData);
}

function isServerInfoToolEnabled() {
    global $serverInfoTool;
    if (!isset($serverInfoTool)) {
        $serverInfoTool = new ServerInfoTool();
    }
    return $serverInfoTool->isEnabled();
}
