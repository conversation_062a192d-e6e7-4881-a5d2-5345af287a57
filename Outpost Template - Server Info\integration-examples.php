<?php
/**
 * Integration Examples for Server Info Tool
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description Examples of how to integrate the server info tool
 */

// This file contains examples of how to integrate the server info tool
// into your existing Outpost template without modifying core files

?>

<!-- EXAMPLE 1: Adding to existing head.php -->
<!-- Add this to your templates/head.php file -->
<?php
// Include Server Info Tool if available
if (file_exists(__DIR__ . '/../server-info-tool/integration.php')) {
    require_once __DIR__ . '/../server-info-tool/integration.php';
    includeServerInfoAssets();
}
?>

<!-- EXAMPLE 2: Adding navigation item -->
<!-- Add this to your templates/navigation.php file -->
<?php if (file_exists(__DIR__ . '/../server-info-tool/integration.php')): ?>
    <?php require_once __DIR__ . '/../server-info-tool/integration.php'; ?>
    <?php echo addServerInfoNavigation(); ?>
<?php endif; ?>

<!-- EXAMPLE 3: Enhanced server modal -->
<!-- Replace your existing server modal with this enhanced version -->
<?php if (file_exists('server-info-tool/integration.php')): ?>
    <?php require_once 'server-info-tool/integration.php'; ?>
    
    <!-- Enhanced Modal with Server Info -->
    <?php foreach ($servers as $serverId => $server): ?>
        <?php echo renderEnhancedServerModal($serverId, $server); ?>
    <?php endforeach; ?>
    
<?php else: ?>
    <!-- Fallback to original modal if server info tool not available -->
    <!-- Your original modal code here -->
<?php endif; ?>

<!-- EXAMPLE 4: Adding server info button to existing server cards -->
<!-- Add this button to your server cards -->
<?php if (file_exists('server-info-tool/integration.php')): ?>
    <?php require_once 'server-info-tool/integration.php'; ?>
    <?php echo addServerInfoButton($serverId); ?>
<?php endif; ?>

<!-- EXAMPLE 5: Dashboard widget -->
<!-- Add this to your admin dashboard -->
<?php if (file_exists('server-info-tool/integration.php')): ?>
    <?php require_once 'server-info-tool/integration.php'; ?>
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5>Server Status</h5>
            </div>
            <div class="card-body">
                <?php echo addServerInfoWidget($servers); ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- EXAMPLE 6: Inline server info in existing pages -->
<!-- Add server info directly to any page -->
<?php if (file_exists('server-info-tool/display.php')): ?>
    <?php require_once 'server-info-tool/display.php'; ?>
    
    <div class="server-details-section">
        <h3>Server Details</h3>
        <?php echo renderServerInfo(0, $servers[0], 'accordion'); ?>
    </div>
<?php endif; ?>

<!-- EXAMPLE 7: Compact info for sidebars -->
<!-- Use compact version for sidebars or small spaces -->
<?php if (file_exists('server-info-tool/display.php')): ?>
    <?php require_once 'server-info-tool/display.php'; ?>
    
    <div class="sidebar-server-info">
        <h4>Quick Server Info</h4>
        <?php foreach ($servers as $id => $server): ?>
            <div class="server-quick-info">
                <h5><?php echo htmlspecialchars($server['name']); ?></h5>
                <?php echo renderCompactServerInfo($id, $server); ?>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- EXAMPLE 8: AJAX loading with JavaScript -->
<script>
// Load server info dynamically
function loadServerInfo(serverId, containerId) {
    if (typeof window.serverInfoTool !== 'undefined') {
        window.serverInfoTool.getServerInfo(serverId).then(data => {
            if (data) {
                document.getElementById(containerId).innerHTML = data.html;
            }
        });
    } else {
        // Fallback AJAX call
        fetch('server-info-tool/api.php?action=get_server_html&server_id=' + serverId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(containerId).innerHTML = data.data.html;
                }
            })
            .catch(error => console.error('Error loading server info:', error));
    }
}

// Example usage
document.addEventListener('DOMContentLoaded', function() {
    // Load server info for server 0 into element with id 'server-info-container'
    loadServerInfo(0, 'server-info-container');
});
</script>

<!-- EXAMPLE 9: Custom styling integration -->
<style>
/* Custom styles that integrate with your theme */
.server-info-container {
    /* Override default styles to match your theme */
    background: var(--your-theme-bg-color);
    border-color: var(--your-theme-border-color);
}

.server-info-section-header {
    background: var(--your-theme-header-bg);
}

.server-info-field-label {
    color: var(--your-theme-text-muted);
}

.server-info-field-value {
    color: var(--your-theme-text-primary);
}

/* Responsive adjustments for your layout */
@media (max-width: 768px) {
    .server-info-container {
        margin: 10px 0;
        border-width: 3px;
    }
}
</style>

<!-- EXAMPLE 10: Conditional loading based on user permissions -->
<?php
// Only show detailed server info to logged-in users
if (isset($_SESSION['steamid']) && file_exists('server-info-tool/display.php')):
    require_once 'server-info-tool/display.php';
?>
    <div class="authenticated-server-info">
        <h3>Detailed Server Information</h3>
        <p class="text-muted">Available to authenticated users</p>
        <?php echo renderServerInfo($serverId, $server); ?>
    </div>
<?php else: ?>
    <div class="basic-server-info">
        <h3>Server Information</h3>
        <p>Please <a href="steamauth/login.php">login with Steam</a> to view detailed server information.</p>
        <!-- Show basic info only -->
        <div class="basic-info">
            <p><strong>Server:</strong> <?php echo htmlspecialchars($server['name']); ?></p>
            <p><strong>Players:</strong> <?php echo $server['players']; ?>/<?php echo $server['maxPlayers']; ?></p>
            <p><strong>Status:</strong> <?php echo $server['online'] ? 'Online' : 'Offline'; ?></p>
        </div>
    </div>
<?php endif; ?>

<!-- EXAMPLE 11: Integration with existing modal system -->
<script>
// Enhance existing modals with server info
function enhanceServerModal(serverId) {
    const modal = document.getElementById('server' + serverId);
    if (modal && typeof window.serverInfoTool !== 'undefined') {
        const modalBody = modal.querySelector('.modal-body');
        if (modalBody) {
            // Add server info section
            const serverInfoDiv = document.createElement('div');
            serverInfoDiv.className = 'server-info-modal-section';
            serverInfoDiv.innerHTML = '<h5>Detailed Information</h5><div id="modal-server-info-' + serverId + '">Loading...</div>';
            modalBody.appendChild(serverInfoDiv);
            
            // Load server info
            window.serverInfoTool.getServerInfo(serverId).then(data => {
                if (data) {
                    document.getElementById('modal-server-info-' + serverId).innerHTML = data.html;
                }
            });
        }
    }
}

// Enhance all server modals
document.addEventListener('DOMContentLoaded', function() {
    <?php foreach ($servers as $id => $server): ?>
        enhanceServerModal(<?php echo $id; ?>);
    <?php endforeach; ?>
});
</script>

<!-- EXAMPLE 12: API integration for external applications -->
<script>
// Example of using the API for external integrations
class ServerInfoAPI {
    constructor(baseUrl = 'server-info-tool/api.php') {
        this.baseUrl = baseUrl;
    }
    
    async getServerInfo(serverId) {
        const response = await fetch(`${this.baseUrl}?action=get_server_info&server_id=${serverId}`);
        const data = await response.json();
        return data.success ? data.data : null;
    }
    
    async refreshCache(serverId) {
        const response = await fetch(`${this.baseUrl}?action=refresh_cache&server_id=${serverId}`);
        const data = await response.json();
        return data.success;
    }
    
    async getConfig() {
        const response = await fetch(`${this.baseUrl}?action=get_config`);
        const data = await response.json();
        return data.success ? data.data : null;
    }
}

// Usage example
const api = new ServerInfoAPI();
api.getServerInfo(0).then(info => {
    console.log('Server info:', info);
});
</script>

<?php
/**
 * INSTALLATION NOTES:
 * 
 * 1. Copy the 'server-info-tool' folder to your Outpost template root directory
 * 
 * 2. Choose one or more integration methods from the examples above
 * 
 * 3. For basic integration, add this to your templates/head.php:
 *    <?php
 *    if (file_exists(__DIR__ . '/../server-info-tool/integration.php')) {
 *        require_once __DIR__ . '/../server-info-tool/integration.php';
 *        includeServerInfoAssets();
 *    }
 *    ?>
 * 
 * 4. Add navigation item to templates/navigation.php:
 *    <?php
 *    if (file_exists(__DIR__ . '/../server-info-tool/integration.php')) {
 *        require_once __DIR__ . '/../server-info-tool/integration.php';
 *        echo addServerInfoNavigation();
 *    }
 *    ?>
 * 
 * 5. Create server-info.php page using the provided example
 * 
 * 6. Configure the tool by editing server-info-tool/config.php
 * 
 * 7. Set proper file permissions:
 *    chmod 755 server-info-tool/
 *    chmod 755 server-info-tool/cache/
 *    chmod 644 server-info-tool/*.php
 * 
 * 8. Test the installation by visiting yoursite.com/server-info.php
 */
?>
