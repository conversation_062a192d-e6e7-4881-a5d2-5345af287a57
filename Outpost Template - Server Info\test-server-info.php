<?php
/**
 * Simple Test File for Server Info Tool
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>Server Info Tool Test</h1>";

// Test 1: Check if files exist
echo "<h2>File Existence Check:</h2>";
echo "server-info-tool/config.php: " . (file_exists('server-info-tool/config.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";
echo "server-info-tool/core.php: " . (file_exists('server-info-tool/core.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";
echo "server-info-tool/display.php: " . (file_exists('server-info-tool/display.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";
echo "server-info-tool/integration.php: " . (file_exists('server-info-tool/integration.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";

// Test 2: Check if core files exist
echo "<h2>Core Template Files Check:</h2>";
echo "steamauth/steamauth.php: " . (file_exists('steamauth/steamauth.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";
echo "core.php: " . (file_exists('core.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";
echo "config.php: " . (file_exists('config.php') ? "✅ EXISTS" : "❌ MISSING") . "<br>";

// Test 3: Try to include core files
echo "<h2>Core Files Loading Test:</h2>";

try {
    if (file_exists('steamauth/steamauth.php')) {
        require_once 'steamauth/steamauth.php';
        echo "steamauth.php: ✅ LOADED<br>";
    } else {
        echo "steamauth.php: ❌ FILE NOT FOUND<br>";
    }
} catch (Exception $e) {
    echo "steamauth.php: ❌ ERROR - " . $e->getMessage() . "<br>";
}

try {
    if (file_exists('core.php')) {
        require_once 'core.php';
        echo "core.php: ✅ LOADED<br>";
    } else {
        echo "core.php: ❌ FILE NOT FOUND<br>";
    }
} catch (Exception $e) {
    echo "core.php: ❌ ERROR - " . $e->getMessage() . "<br>";
}

// Test 4: Try to load server info tool
echo "<h2>Server Info Tool Loading Test:</h2>";

try {
    if (file_exists('server-info-tool/integration.php')) {
        require_once 'server-info-tool/integration.php';
        echo "integration.php: ✅ LOADED<br>";
        
        if (function_exists('doesServerInfoToolExist')) {
            echo "doesServerInfoToolExist function: ✅ EXISTS<br>";
            echo "Tool enabled: " . (doesServerInfoToolExist() ? "✅ YES" : "❌ NO") . "<br>";
        } else {
            echo "doesServerInfoToolExist function: ❌ NOT FOUND<br>";
        }
    } else {
        echo "integration.php: ❌ FILE NOT FOUND<br>";
    }
} catch (Exception $e) {
    echo "integration.php: ❌ ERROR - " . $e->getMessage() . "<br>";
}

// Test 5: Check server data
echo "<h2>Server Data Check:</h2>";

if (isset($servers) && is_array($servers)) {
    echo "Servers variable: ✅ EXISTS<br>";
    echo "Number of servers: " . count($servers) . "<br>";
    
    foreach ($servers as $id => $server) {
        echo "Server $id: " . (isset($server['name']) ? $server['name'] : 'No name') . "<br>";
    }
} else {
    echo "Servers variable: ❌ NOT FOUND OR NOT ARRAY<br>";
}

// Test 6: Check config
echo "<h2>Config Check:</h2>";

if (isset($config) && is_array($config)) {
    echo "Config variable: ✅ EXISTS<br>";
    echo "Site title: " . (isset($config['title']) ? $config['title'] : 'Not set') . "<br>";
} else {
    echo "Config variable: ❌ NOT FOUND OR NOT ARRAY<br>";
}

// Test 7: PHP Info
echo "<h2>PHP Environment:</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current working directory: " . getcwd() . "<br>";
echo "Script filename: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";

// Test 8: Directory permissions
echo "<h2>Directory Permissions:</h2>";
echo "Current directory writable: " . (is_writable('.') ? "✅ YES" : "❌ NO") . "<br>";
if (is_dir('server-info-tool')) {
    echo "server-info-tool directory writable: " . (is_writable('server-info-tool') ? "✅ YES" : "❌ NO") . "<br>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Check all the items above for any ❌ errors</li>";
echo "<li>If files are missing, re-upload them</li>";
echo "<li>If there are permission errors, run: <code>chmod 755 server-info-tool/</code></li>";
echo "<li>If core files are missing, make sure you're in the right directory</li>";
echo "</ol>";

echo "<p><a href='server-info.php'>Try server-info.php again</a></p>";
?>
