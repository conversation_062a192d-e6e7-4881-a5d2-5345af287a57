<?php

/**
 * Server Information Tool API
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description RESTful API for server information
 */

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once __DIR__ . '/core.php';
require_once __DIR__ . '/display.php';

class ServerInfoAPI {
    
    private $serverInfoTool;
    private $config;
    private $rateLimiter;
    
    public function __construct() {
        global $serverInfoTool;
        $this->serverInfoTool = $serverInfoTool;
        $this->config = $this->serverInfoTool->getConfig();
        $this->rateLimiter = new RateLimiter();
    }
    
    /**
     * Handle API requests
     */
    public function handleRequest() {
        try {
            // Check if tool is enabled
            if (!$this->serverInfoTool->isEnabled()) {
                return $this->error('Server Info Tool is disabled', 503);
            }
            
            // Rate limiting
            if ($this->config['security']['rate_limit']['enabled']) {
                if (!$this->rateLimiter->checkLimit()) {
                    return $this->error('Rate limit exceeded', 429);
                }
            }
            
            // Get action
            $action = $_GET['action'] ?? $_POST['action'] ?? '';
            
            if (empty($action)) {
                return $this->error('No action specified', 400);
            }
            
            // Route to appropriate method
            switch ($action) {
                case 'get_server_info':
                    return $this->getServerInfo();
                case 'get_server_html':
                    return $this->getServerHTML();
                case 'get_compact_info':
                    return $this->getCompactInfo();
                case 'refresh_cache':
                    return $this->refreshCache();
                case 'get_config':
                    return $this->getConfig();
                case 'get_cache_stats':
                    return $this->getCacheStats();
                default:
                    return $this->error('Invalid action', 400);
            }
            
        } catch (Exception $e) {
            return $this->error('Internal server error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get server information data
     */
    private function getServerInfo() {
        $serverId = $this->getServerId();
        if ($serverId === null) {
            return $this->error('Server ID is required', 400);
        }
        
        $serverData = $this->getServerData($serverId);
        $info = $this->serverInfoTool->getServerInfo($serverId, $serverData);
        
        if ($info === null) {
            return $this->error('Server not found or tool disabled', 404);
        }
        
        return $this->success($info);
    }
    
    /**
     * Get server information as HTML
     */
    private function getServerHTML() {
        $serverId = $this->getServerId();
        if ($serverId === null) {
            return $this->error('Server ID is required', 400);
        }
        
        $layout = $_GET['layout'] ?? $this->config['display']['layout'];
        $serverData = $this->getServerData($serverId);
        
        $html = renderServerInfo($serverId, $serverData, $layout);
        
        if (empty($html)) {
            return $this->error('Failed to generate HTML', 500);
        }
        
        return $this->success(['html' => $html]);
    }
    
    /**
     * Get compact server information
     */
    private function getCompactInfo() {
        $serverId = $this->getServerId();
        if ($serverId === null) {
            return $this->error('Server ID is required', 400);
        }
        
        $serverData = $this->getServerData($serverId);
        $html = renderCompactServerInfo($serverId, $serverData);
        
        return $this->success(['html' => $html]);
    }
    
    /**
     * Refresh cache for a server
     */
    private function refreshCache() {
        $serverId = $this->getServerId();
        if ($serverId === null) {
            return $this->error('Server ID is required', 400);
        }
        
        $cacheKey = "server_info_{$serverId}";
        $this->serverInfoTool->clearCache($cacheKey);
        
        return $this->success(['message' => 'Cache refreshed successfully']);
    }
    
    /**
     * Get public configuration
     */
    private function getConfig() {
        $config = $this->config;
        
        // Remove sensitive information
        unset($config['integrations']);
        unset($config['security']);
        unset($config['cache']['path']);
        
        return $this->success($config);
    }
    
    /**
     * Get cache statistics
     */
    private function getCacheStats() {
        $stats = $this->serverInfoTool->getCacheStats();
        
        if ($stats === null) {
            return $this->error('Cache is disabled', 400);
        }
        
        // Remove sensitive path information
        unset($stats['cache_dir']);
        
        return $this->success($stats);
    }
    
    /**
     * Get server ID from request
     */
    private function getServerId() {
        $serverId = $_GET['server_id'] ?? $_POST['server_id'] ?? null;
        
        if ($serverId === null) {
            return null;
        }
        
        return (int) $serverId;
    }
    
    /**
     * Get server data (mock implementation)
     */
    private function getServerData($serverId) {
        // This should be replaced with actual server data fetching
        // For now, return mock data or try to get from global $servers
        global $servers;
        
        if (isset($servers[$serverId])) {
            return $servers[$serverId];
        }
        
        // Mock data for testing
        return [
            'name' => 'Test Server ' . $serverId,
            'ip' => '127.0.0.1',
            'port' => 28015,
            'map' => 'Procedural Map',
            'players' => rand(0, 100),
            'maxPlayers' => 100,
            'online' => true,
        ];
    }
    
    /**
     * Return success response
     */
    private function success($data = null) {
        $response = ['success' => true];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        echo json_encode($response);
        exit;
    }
    
    /**
     * Return error response
     */
    private function error($message, $code = 400) {
        http_response_code($code);
        
        echo json_encode([
            'success' => false,
            'error' => $message,
            'code' => $code
        ]);
        exit;
    }
}

/**
 * Simple rate limiter
 */
class RateLimiter {
    
    private $config;
    
    public function __construct() {
        global $serverInfoTool;
        $this->config = $serverInfoTool->getConfig()['security']['rate_limit'];
    }
    
    /**
     * Check if request is within rate limit
     */
    public function checkLimit() {
        if (!$this->config['enabled']) {
            return true;
        }
        
        $ip = $this->getClientIP();
        $key = 'rate_limit_' . md5($ip);
        $cacheFile = __DIR__ . '/cache/' . $key . '.cache';
        
        $now = time();
        $windowStart = $now - 60; // 1 minute window
        
        // Get current requests
        $requests = [];
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $requests = json_decode($data, true) ?: [];
        }
        
        // Remove old requests
        $requests = array_filter($requests, function($timestamp) use ($windowStart) {
            return $timestamp > $windowStart;
        });
        
        // Check limit
        if (count($requests) >= $this->config['requests_per_minute']) {
            return false;
        }
        
        // Add current request
        $requests[] = $now;
        
        // Save to cache
        file_put_contents($cacheFile, json_encode($requests));
        
        return true;
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return '127.0.0.1';
    }
}

// Handle the request
$api = new ServerInfoAPI();
$api->handleRequest();
