<?php
/**
 * Standalone Server Information Page
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description Example standalone page for server information
 */

// Start session and include required files
session_start();

// Include Outpost template core files (adjust paths as needed)
require_once 'steamauth/steamauth.php';
require_once 'core.php';

// Include server info tool
require_once 'server-info-tool/integration.php';

// Check if server info tool is available
if (!doesServerInfoToolExist()) {
    header('Location: index.php');
    exit;
}

// Get specific server ID if provided
$serverId = isset($_GET['server']) ? (int)$_GET['server'] : null;
$selectedServer = null;

if ($serverId !== null && isset($servers[$serverId])) {
    $selectedServer = $servers[$serverId];
}

// Page title
$pageTitle = $selectedServer ? $selectedServer['name'] . ' - Server Information' : 'Server Information';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - <?php echo htmlspecialchars($siteName); ?></title>
    
    <!-- Include Outpost template head -->
    <?php include 'templates/head.php'; ?>
    
    <!-- Include server info tool assets -->
    <?php includeServerInfoAssets(); ?>
    
    <style>
        .server-info-page-header {
            background: linear-gradient(135deg, rgb(28, 29, 23) 0%, rgb(35, 36, 30) 100%);
            padding: 60px 0;
            margin-bottom: 40px;
            border-bottom: 3px solid rgb(201, 69, 47);
        }
        
        .server-info-page-title {
            color: rgb(198, 192, 186);
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .server-info-page-subtitle {
            color: rgb(142, 143, 138);
            font-size: 1.2rem;
            text-align: center;
            margin-top: 10px;
        }
        
        .server-info-page-card {
            background: rgb(31, 32, 26);
            border: 6px solid rgb(31, 32, 26);
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .server-info-page-card h3 {
            background: rgb(28, 29, 23);
            color: rgb(198, 192, 186);
            padding: 20px;
            margin: 0;
            font-size: 1.5rem;
            font-weight: bold;
            text-transform: uppercase;
            border-bottom: 2px solid rgb(41, 49, 41);
        }
        
        .server-selector {
            background: rgb(31, 32, 26);
            border: 6px solid rgb(31, 32, 26);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .server-selector h4 {
            color: rgb(198, 192, 186);
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .server-selector-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .server-selector-item {
            background: rgb(35, 36, 30);
            border: 2px solid rgb(41, 49, 41);
            border-radius: 6px;
            padding: 15px;
            text-decoration: none;
            color: rgb(173, 168, 162);
            transition: all 0.3s ease;
            display: block;
        }
        
        .server-selector-item:hover {
            border-color: rgb(201, 69, 47);
            background: rgb(38, 39, 33);
            color: rgb(198, 192, 186);
            text-decoration: none;
        }
        
        .server-selector-item.active {
            border-color: rgb(201, 69, 47);
            background: rgb(201, 69, 47);
            color: white;
        }
        
        .server-selector-name {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .server-selector-status {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .breadcrumb-custom {
            background: rgb(35, 36, 30);
            border: 1px solid rgb(41, 49, 41);
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 30px;
        }
        
        .breadcrumb-custom a {
            color: rgb(201, 69, 47);
            text-decoration: none;
        }
        
        .breadcrumb-custom a:hover {
            text-decoration: underline;
        }
        
        .breadcrumb-custom .current {
            color: rgb(173, 168, 162);
        }
        
        @media (max-width: 768px) {
            .server-info-page-title {
                font-size: 2rem;
            }
            
            .server-selector-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Include navigation -->
    <?php include 'templates/navigation.php'; ?>
    
    <!-- Page Header -->
    <div class="server-info-page-header">
        <div class="container">
            <h1 class="server-info-page-title"><?php echo htmlspecialchars($pageTitle); ?></h1>
            <?php if ($selectedServer): ?>
                <p class="server-info-page-subtitle">Detailed information and statistics</p>
            <?php else: ?>
                <p class="server-info-page-subtitle">Choose a server to view detailed information</p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="container">
        <!-- Breadcrumb -->
        <div class="breadcrumb-custom">
            <a href="index.php">Home</a> / 
            <?php if ($selectedServer): ?>
                <a href="server-info.php">Server Information</a> / 
                <span class="current"><?php echo htmlspecialchars($selectedServer['name']); ?></span>
            <?php else: ?>
                <span class="current">Server Information</span>
            <?php endif; ?>
        </div>
        
        <?php if ($selectedServer): ?>
            <!-- Single Server View -->
            <div class="row justify-content-center">
                <div class="col-12 col-lg-10">
                    <div class="server-info-page-card">
                        <h3><?php echo htmlspecialchars($selectedServer['name']); ?></h3>
                        <div style="padding: 0;">
                            <?php echo renderServerInfo($serverId, $selectedServer); ?>
                        </div>
                    </div>
                    
                    <!-- Back to all servers -->
                    <div class="text-center">
                        <a href="server-info.php" class="btn btn-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                            </svg>
                            Back to All Servers
                        </a>
                    </div>
                </div>
            </div>
            
        <?php else: ?>
            <!-- Server Selector -->
            <div class="server-selector">
                <h4>Select a Server</h4>
                <div class="server-selector-grid">
                    <?php foreach ($servers as $id => $server): ?>
                        <a href="server-info.php?server=<?php echo $id; ?>" class="server-selector-item">
                            <div class="server-selector-name"><?php echo htmlspecialchars($server['name']); ?></div>
                            <div class="server-selector-status">
                                <?php if ($server['online']): ?>
                                    <span style="color: #28a745;">● Online</span> - 
                                    <?php echo $server['players']; ?>/<?php echo $server['maxPlayers']; ?> players
                                <?php else: ?>
                                    <span style="color: #dc3545;">● Offline</span>
                                <?php endif; ?>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- All Servers Overview -->
            <div class="row">
                <?php foreach ($servers as $id => $server): ?>
                    <div class="col-12 col-lg-6 mb-4">
                        <div class="server-info-page-card">
                            <h3>
                                <?php echo htmlspecialchars($server['name']); ?>
                                <a href="server-info.php?server=<?php echo $id; ?>" class="btn btn-sm btn-outline-light float-end">
                                    View Details
                                </a>
                            </h3>
                            <div style="padding: 0;">
                                <?php echo renderCompactServerInfo($id, $server); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Include footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Initialize server info tool -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize server info tool if not already initialized
            if (!window.serverInfoTool && document.querySelector('.server-info-container')) {
                window.serverInfoTool = new ServerInfoTool({
                    autoRefresh: true,
                    refreshInterval: 30000
                });
            }
        });
    </script>
</body>
</html>
