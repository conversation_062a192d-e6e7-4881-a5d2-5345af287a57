<?php

/**
 * Server Information Tool API
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description API endpoints for dynamic content loading
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once __DIR__ . '/core.php';
require_once __DIR__ . '/display.php';
require_once __DIR__ . '/../core.php'; // Main Outpost core

// Rate limiting
session_start();
$rate_limit_key = 'serverinfo_api_' . $_SERVER['REMOTE_ADDR'];
$current_time = time();
$rate_limit_window = 60; // 1 minute
$max_requests = 60;

if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = [];
}

// Clean old requests
$_SESSION[$rate_limit_key] = array_filter($_SESSION[$rate_limit_key], function($timestamp) use ($current_time, $rate_limit_window) {
    return ($current_time - $timestamp) < $rate_limit_window;
});

// Check rate limit
if (count($_SESSION[$rate_limit_key]) >= $max_requests) {
    http_response_code(429);
    echo json_encode(['error' => 'Rate limit exceeded']);
    exit;
}

// Add current request
$_SESSION[$rate_limit_key][] = $current_time;

// Get request parameters
$action = $_GET['action'] ?? '';
$serverId = $_GET['server_id'] ?? null;
$section = $_GET['section'] ?? null;

try {
    global $serverInfoTool, $serverInfoDisplay, $servers;
    
    switch ($action) {
        case 'get_server_info':
            if ($serverId === null) {
                throw new Exception('Server ID is required');
            }
            
            // Get server data from main system
            $serverData = null;
            if (isset($servers[$serverId])) {
                $serverData = $servers[$serverId];
            }
            
            $serverInfo = $serverInfoTool->getServerInfo($serverId, $serverData);
            
            if (!$serverInfo) {
                throw new Exception('Server information not available');
            }
            
            echo json_encode([
                'success' => true,
                'data' => $serverInfo,
                'timestamp' => time()
            ]);
            break;
            
        case 'get_server_html':
            if ($serverId === null) {
                throw new Exception('Server ID is required');
            }
            
            // Get server data from main system
            $serverData = null;
            if (isset($servers[$serverId])) {
                $serverData = $servers[$serverId];
            }
            
            $html = $serverInfoDisplay->renderServerInfo($serverId, $serverData);
            
            echo json_encode([
                'success' => true,
                'html' => $html,
                'timestamp' => time()
            ]);
            break;
            
        case 'get_compact_info':
            if ($serverId === null) {
                throw new Exception('Server ID is required');
            }
            
            // Get server data from main system
            $serverData = null;
            if (isset($servers[$serverId])) {
                $serverData = $servers[$serverId];
            }
            
            $html = $serverInfoDisplay->renderCompactInfo($serverId, $serverData);
            
            echo json_encode([
                'success' => true,
                'html' => $html,
                'timestamp' => time()
            ]);
            break;
            
        case 'get_section':
            if ($serverId === null || $section === null) {
                throw new Exception('Server ID and section are required');
            }
            
            // Get server data from main system
            $serverData = null;
            if (isset($servers[$serverId])) {
                $serverData = $servers[$serverId];
            }
            
            $serverInfo = $serverInfoTool->getServerInfo($serverId, $serverData);
            
            if (!$serverInfo || !isset($serverInfo['sections'][$section])) {
                throw new Exception('Section not found');
            }
            
            echo json_encode([
                'success' => true,
                'data' => $serverInfo['sections'][$section],
                'timestamp' => time()
            ]);
            break;
            
        case 'get_all_servers':
            $allServersInfo = [];
            
            foreach ($servers as $id => $serverData) {
                $serverInfo = $serverInfoTool->getServerInfo($id, $serverData);
                if ($serverInfo) {
                    $allServersInfo[$id] = $serverInfo;
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => $allServersInfo,
                'timestamp' => time()
            ]);
            break;
            
        case 'get_config':
            $config = $serverInfoTool->getConfig();
            
            // Remove sensitive information
            unset($config['integrations']);
            unset($config['security']);
            
            echo json_encode([
                'success' => true,
                'data' => $config,
                'timestamp' => time()
            ]);
            break;
            
        case 'refresh_cache':
            if ($serverId !== null) {
                // Clear cache for specific server
                $cacheKey = "server_info_{$serverId}";
                $serverInfoTool->clearCache($cacheKey);
            } else {
                // Clear all cache
                $serverInfoTool->cleanupCache();
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Cache refreshed successfully',
                'timestamp' => time()
            ]);
            break;
            
        case 'get_status':
            $status = [
                'enabled' => $serverInfoTool->isEnabled(),
                'version' => $serverInfoTool->getConfig()['version'],
                'cache_enabled' => $serverInfoTool->getConfig()['cache']['enabled'],
                'total_servers' => count($servers),
                'timestamp' => time()
            ];
            
            echo json_encode([
                'success' => true,
                'data' => $status,
                'timestamp' => time()
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => time()
    ]);
}

// Add cache clearing method to ServerInfoTool class
if (!method_exists('ServerInfoTool', 'clearCache')) {
    // Extend the ServerInfoTool class with additional methods
    class ExtendedServerInfoTool extends ServerInfoTool {
        public function clearCache($key = null) {
            if (!$this->config['cache']['enabled']) {
                return;
            }
            
            if ($key) {
                $file = $this->cache_dir . md5($key) . '.cache';
                if (file_exists($file)) {
                    unlink($file);
                }
            } else {
                $this->cleanupCache();
            }
        }
        
        public function getCacheStats() {
            if (!$this->config['cache']['enabled']) {
                return null;
            }
            
            $files = glob($this->cache_dir . '*.cache');
            $totalSize = 0;
            $validFiles = 0;
            $expiredFiles = 0;
            $now = time();
            
            foreach ($files as $file) {
                $totalSize += filesize($file);
                $data = file_get_contents($file);
                $cached = unserialize($data);
                
                if ($cached['expires'] > $now) {
                    $validFiles++;
                } else {
                    $expiredFiles++;
                }
            }
            
            return [
                'total_files' => count($files),
                'valid_files' => $validFiles,
                'expired_files' => $expiredFiles,
                'total_size' => $totalSize,
                'cache_dir' => $this->cache_dir
            ];
        }
        
        public function getFieldMappings() {
            return [
                'overview' => [
                    'name' => 'Server Name',
                    'map' => 'Current Map',
                    'players' => 'Players Online',
                    'max_players' => 'Max Players',
                    'queue' => 'Queue Length',
                    'uptime' => 'Server Uptime',
                    'last_wipe' => 'Last Wipe Date',
                    'next_wipe' => 'Next Wipe Date'
                ],
                'connection' => [
                    'ip' => 'Server IP Address',
                    'port' => 'Server Port',
                    'connect_command' => 'Console Connect Command',
                    'steam_connect' => 'Steam Connect Link',
                    'battlemetrics' => 'BattleMetrics Link'
                ],
                'statistics' => [
                    'avg_players' => 'Average Players (24h)',
                    'peak_players' => 'Peak Players (24h)',
                    'total_playtime' => 'Total Playtime',
                    'unique_players' => 'Unique Players',
                    'server_fps' => 'Server Performance (FPS)',
                    'entity_count' => 'Entity Count'
                ],
                'features' => [
                    'gather_rate' => 'Resource Gather Rate',
                    'craft_rate' => 'Crafting Speed',
                    'stack_size' => 'Item Stack Size',
                    'decay_rate' => 'Building Decay Rate',
                    'pvp_enabled' => 'PvP Enabled',
                    'pve_enabled' => 'PvE Enabled',
                    'kits_enabled' => 'Starter Kits Available',
                    'tp_enabled' => 'Teleportation Enabled'
                ],
                'economy' => [
                    'currency' => 'In-game Currency',
                    'shop_enabled' => 'Shop System Available',
                    'shop_url' => 'Shop Website URL',
                    'vip_available' => 'VIP Membership Available',
                    'vip_url' => 'VIP Store URL'
                ]
            ];
        }
        
        public function validateServerData($serverData) {
            $required_fields = ['ip', 'port', 'name'];
            $missing_fields = [];
            
            foreach ($required_fields as $field) {
                if (!isset($serverData[$field]) || empty($serverData[$field])) {
                    $missing_fields[] = $field;
                }
            }
            
            return [
                'valid' => empty($missing_fields),
                'missing_fields' => $missing_fields,
                'has_battlemetrics' => !empty($serverData['battlemetrics']),
                'has_store' => !empty($serverData['store']),
                'is_online' => $serverData['online'] ?? false
            ];
        }
    }
    
    // Replace the global instance
    $serverInfoTool = new ExtendedServerInfoTool();
}
