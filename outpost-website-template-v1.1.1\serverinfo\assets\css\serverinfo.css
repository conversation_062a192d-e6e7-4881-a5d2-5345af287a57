/*
 * ============================================
 *        SERVER INFORMATION TOOL CSS
 * ============================================
 * <AUTHOR> Agent
 * @version 1.0.0
 * @description Custom styles for server info tool
 * ============================================
 */

/* Main Container */
.server-info-container {
    background: rgb(31, 32, 26);
    border: 6px solid rgb(31, 32, 26);
    border-radius: 8px;
    margin: 20px 0;
    overflow: hidden;
    font-family: "Roboto Condensed", Arial, sans-serif;
}

.server-info-header {
    background: rgb(28, 29, 23);
    padding: 15px 20px;
    border-bottom: 2px solid rgb(41, 49, 41);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.server-info-title {
    color: rgb(198, 192, 186);
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    text-transform: uppercase;
}

.server-info-timestamp {
    color: rgb(142, 143, 138);
    font-size: 14px;
    font-style: italic;
}

/* Dropdown Layout */
.server-info-section {
    border-bottom: 1px solid rgb(41, 49, 41);
    transition: all 0.3s ease;
}

.server-info-section:last-child {
    border-bottom: none;
}

.server-info-section-header {
    background: rgb(35, 36, 30);
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
    user-select: none;
}

.server-info-section-header:hover {
    background: rgb(38, 39, 33);
}

.server-info-section-title {
    display: flex;
    align-items: center;
    color: rgb(198, 192, 186);
    font-size: 18px;
    font-weight: bold;
}

.server-info-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    opacity: 0.8;
}

.server-info-toggle {
    color: rgb(142, 143, 138);
    transition: transform 0.3s ease;
}

.server-info-section.open .server-info-toggle {
    transform: rotate(180deg);
}

.toggle-icon::before {
    content: "▼";
    font-size: 12px;
}

.server-info-section-content {
    background: rgb(31, 32, 26);
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.server-info-section-content.collapse:not(.show) {
    max-height: 0;
}

.server-info-section-content.collapse.show {
    max-height: 1000px;
}

.server-info-fields {
    padding: 20px;
}

/* Field Styling */
.server-info-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(41, 49, 41, 0.5);
}

.server-info-field:last-child {
    border-bottom: none;
}

.server-info-field-label {
    color: rgb(142, 143, 138);
    font-weight: 500;
    min-width: 150px;
    flex-shrink: 0;
}

.server-info-field-value {
    color: rgb(173, 168, 162);
    text-align: right;
    flex-grow: 1;
    word-break: break-word;
}

.server-info-empty {
    color: rgb(100, 100, 100);
    font-style: italic;
}

.server-info-boolean.enabled {
    color: rgb(166, 205, 99);
    font-weight: bold;
}

.server-info-boolean.disabled {
    color: rgb(150, 47, 32);
    font-weight: bold;
}

.server-info-textarea {
    background: rgb(28, 29, 23);
    padding: 10px;
    border-radius: 4px;
    border: 1px solid rgb(41, 49, 41);
    max-height: 150px;
    overflow-y: auto;
    line-height: 1.4;
    text-align: left;
    width: 100%;
    margin-top: 5px;
}

/* Copy Field */
.server-info-copy-field {
    display: flex;
    gap: 10px;
    align-items: center;
    width: 100%;
}

.server-info-copy-input {
    background: rgb(28, 29, 23);
    border: 1px solid rgb(41, 49, 41);
    color: rgb(173, 168, 162);
    padding: 8px 12px;
    border-radius: 4px;
    flex-grow: 1;
    font-family: monospace;
    font-size: 14px;
}

.server-info-copy-btn {
    background: rgb(201, 69, 47);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s ease;
    flex-shrink: 0;
}

.server-info-copy-btn:hover {
    background: rgb(181, 49, 27);
}

.server-info-copy-btn:active {
    background: rgb(161, 29, 7);
}

/* Tabs Layout */
.server-info-tabs-container {
    background: rgb(31, 32, 26);
    border: 6px solid rgb(31, 32, 26);
    border-radius: 8px;
    margin: 20px 0;
    overflow: hidden;
}

.server-info-tabs-nav {
    display: flex;
    background: rgb(28, 29, 23);
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 2px solid rgb(41, 49, 41);
    overflow-x: auto;
}

.server-info-tab-item {
    flex-shrink: 0;
}

.server-info-tab-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgb(142, 143, 138);
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.server-info-tab-item a:hover {
    background: rgb(35, 36, 30);
    color: rgb(173, 168, 162);
}

.server-info-tab-item.active a {
    color: rgb(198, 192, 186);
    border-bottom-color: rgb(201, 69, 47);
    background: rgb(35, 36, 30);
}

.server-info-tabs-content {
    padding: 20px;
}

.server-info-tab-pane {
    display: none;
}

.server-info-tab-pane.active {
    display: block;
}

/* Accordion Layout */
.server-info-accordion-container {
    background: rgb(31, 32, 26);
    border: 6px solid rgb(31, 32, 26);
    border-radius: 8px;
    margin: 20px 0;
    overflow: hidden;
}

.server-info-accordion-item {
    border-bottom: 1px solid rgb(41, 49, 41);
}

.server-info-accordion-item:last-child {
    border-bottom: none;
}

.server-info-accordion-header {
    background: rgb(35, 36, 30);
    padding: 15px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgb(198, 192, 186);
    font-size: 18px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.server-info-accordion-header:hover {
    background: rgb(38, 39, 33);
}

.server-info-accordion-toggle::before {
    content: "▼";
    font-size: 12px;
    transition: transform 0.3s ease;
}

.server-info-accordion-content.show + .server-info-accordion-item .server-info-accordion-toggle::before {
    transform: rotate(180deg);
}

.server-info-accordion-content {
    background: rgb(31, 32, 26);
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.server-info-accordion-content.show {
    max-height: 1000px;
    padding: 20px;
}

/* Compact Layout */
.server-info-compact {
    background: rgba(31, 32, 26, 0.9);
    padding: 15px;
    border-radius: 6px;
    border: 1px solid rgb(41, 49, 41);
}

.server-info-compact-field {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.server-info-compact-field:last-child {
    margin-bottom: 0;
}

.server-info-compact-field .label {
    color: rgb(142, 143, 138);
    font-weight: 500;
}

.server-info-compact-field .value {
    color: rgb(173, 168, 162);
    font-weight: bold;
}

/* Icons */
.icon-server::before { content: "🖥️"; }
.icon-link::before { content: "🔗"; }
.icon-chart::before { content: "📊"; }
.icon-rules::before { content: "📋"; }
.icon-features::before { content: "⚙️"; }
.icon-shop::before { content: "🛒"; }
.icon-custom::before { content: "🔧"; }

/* Loading States */
.server-info-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.server-info-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgb(142, 143, 138);
    border-top: 2px solid rgb(201, 69, 47);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .server-info-field {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .server-info-field-label {
        min-width: auto;
    }
    
    .server-info-field-value {
        text-align: left;
        width: 100%;
    }
    
    .server-info-copy-field {
        flex-direction: column;
        gap: 8px;
    }
    
    .server-info-tabs-nav {
        flex-wrap: wrap;
    }
    
    .server-info-tab-item {
        flex: 1;
        min-width: 120px;
    }
    
    .server-info-section-header,
    .server-info-accordion-header {
        padding: 12px 15px;
        font-size: 16px;
    }
    
    .server-info-fields {
        padding: 15px;
    }
}

/* Dark/Light Theme Support */
.server-info-container[data-theme="light"] {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #212529;
}

.server-info-container[data-theme="light"] .server-info-header {
    background: #e9ecef;
    border-bottom-color: #dee2e6;
}

.server-info-container[data-theme="light"] .server-info-title {
    color: #212529;
}

.server-info-container[data-theme="light"] .server-info-timestamp {
    color: #6c757d;
}

.server-info-container[data-theme="light"] .server-info-section-header {
    background: #f1f3f4;
    color: #212529;
}

.server-info-container[data-theme="light"] .server-info-section-header:hover {
    background: #e9ecef;
}

.server-info-container[data-theme="light"] .server-info-field-label {
    color: #495057;
}

.server-info-container[data-theme="light"] .server-info-field-value {
    color: #212529;
}

/* Animation Classes */
.server-info-fade-in {
    animation: fadeIn 0.5s ease-in;
}

.server-info-slide-down {
    animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideDown {
    from { 
        opacity: 0;
        transform: translateY(-10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}
