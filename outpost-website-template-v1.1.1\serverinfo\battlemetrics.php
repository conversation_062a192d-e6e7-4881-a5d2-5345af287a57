<?php

/**
 * BattleMetrics Integration for Server Information Tool
 * <AUTHOR> Agent
 * @version 1.0.0
 */

class BattleMetricsIntegration {
    
    private $config;
    private $cache_dir;
    private $api_base = 'https://api.battlemetrics.com';
    
    public function __construct($config) {
        $this->config = $config;
        $this->cache_dir = $config['cache']['path'] . 'battlemetrics/';
        $this->ensureCacheDirectory();
    }
    
    /**
     * Get server information from BattleMetrics
     */
    public function getServerInfo($battlemetricsId) {
        if (!$this->config['integrations']['battlemetrics']['enabled']) {
            return null;
        }
        
        $cacheKey = "bm_server_{$battlemetricsId}";
        
        // Try cache first
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // Fetch from API
        $url = $this->api_base . "/servers/{$battlemetricsId}";
        $data = $this->makeApiRequest($url);
        
        if (!$data) {
            return null;
        }
        
        $serverInfo = $this->parseServerData($data);
        
        // Cache the result
        $this->saveToCache($cacheKey, $serverInfo);
        
        return $serverInfo;
    }
    
    /**
     * Get server statistics from BattleMetrics
     */
    public function getServerStats($battlemetricsId, $timeframe = '24h') {
        if (!$this->config['integrations']['battlemetrics']['enabled']) {
            return null;
        }
        
        $cacheKey = "bm_stats_{$battlemetricsId}_{$timeframe}";
        
        // Try cache first
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // Fetch player count history
        $url = $this->api_base . "/servers/{$battlemetricsId}/player-count-history";
        $params = [
            'start' => date('c', strtotime("-{$timeframe}")),
            'end' => date('c'),
            'resolution' => '60'
        ];
        
        $url .= '?' . http_build_query($params);
        $data = $this->makeApiRequest($url);
        
        if (!$data) {
            return null;
        }
        
        $stats = $this->parseStatsData($data);
        
        // Cache the result
        $this->saveToCache($cacheKey, $stats);
        
        return $stats;
    }
    
    /**
     * Get server player list
     */
    public function getServerPlayers($battlemetricsId) {
        if (!$this->config['integrations']['battlemetrics']['enabled']) {
            return null;
        }
        
        $cacheKey = "bm_players_{$battlemetricsId}";
        
        // Try cache first (shorter cache for player data)
        $cached = $this->getFromCache($cacheKey, 60); // 1 minute cache
        if ($cached !== null) {
            return $cached;
        }
        
        $url = $this->api_base . "/servers/{$battlemetricsId}/relationships/players";
        $data = $this->makeApiRequest($url);
        
        if (!$data) {
            return null;
        }
        
        $players = $this->parsePlayersData($data);
        
        // Cache the result
        $this->saveToCache($cacheKey, $players, 60);
        
        return $players;
    }
    
    /**
     * Make API request to BattleMetrics
     */
    private function makeApiRequest($url) {
        $headers = [
            'Accept: application/json',
            'User-Agent: Outpost-ServerInfo/1.0'
        ];
        
        // Add API key if available
        if (!empty($this->config['integrations']['battlemetrics']['api_key'])) {
            $headers[] = 'Authorization: Bearer ' . $this->config['integrations']['battlemetrics']['api_key'];
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("BattleMetrics API Error: {$error}");
            return null;
        }
        
        if ($httpCode !== 200) {
            error_log("BattleMetrics API HTTP Error: {$httpCode}");
            return null;
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("BattleMetrics API JSON Error: " . json_last_error_msg());
            return null;
        }
        
        return $data;
    }
    
    /**
     * Parse server data from BattleMetrics response
     */
    private function parseServerData($data) {
        if (!isset($data['data'])) {
            return null;
        }
        
        $server = $data['data'];
        $attributes = $server['attributes'] ?? [];
        
        return [
            'id' => $server['id'],
            'name' => $attributes['name'] ?? 'Unknown',
            'status' => $attributes['status'] ?? 'unknown',
            'players' => $attributes['players'] ?? 0,
            'maxPlayers' => $attributes['maxPlayers'] ?? 0,
            'queue' => $attributes['details']['rust_queued_players'] ?? 0,
            'map' => $attributes['details']['map'] ?? 'Unknown',
            'fps' => $attributes['details']['rust_fps'] ?? null,
            'entityCount' => $attributes['details']['rust_entity_count'] ?? null,
            'uptime' => $attributes['details']['rust_uptime'] ?? null,
            'lastWipe' => $attributes['details']['rust_last_wipe'] ?? null,
            'nextWipe' => $attributes['details']['rust_next_wipe'] ?? null,
            'ip' => $attributes['ip'] ?? null,
            'port' => $attributes['port'] ?? null,
            'country' => $attributes['country'] ?? null,
            'rank' => $attributes['rank'] ?? null,
            'website' => $attributes['details']['rust_website'] ?? null,
            'description' => $attributes['details']['rust_description'] ?? null,
            'headerImage' => $attributes['details']['rust_headerimage'] ?? null,
            'tags' => $attributes['details']['rust_tags'] ?? [],
            'modded' => $attributes['details']['rust_modded'] ?? false,
            'official' => $attributes['details']['rust_official'] ?? false,
            'pve' => $attributes['details']['rust_pve'] ?? false,
            'updated_at' => $attributes['updatedAt'] ?? null
        ];
    }
    
    /**
     * Parse statistics data from BattleMetrics response
     */
    private function parseStatsData($data) {
        if (!isset($data['data'])) {
            return null;
        }
        
        $playerCounts = [];
        $totalPlayers = 0;
        $maxPlayers = 0;
        $minPlayers = PHP_INT_MAX;
        $dataPoints = 0;
        
        foreach ($data['data'] as $point) {
            $timestamp = strtotime($point['timestamp']);
            $players = (int)$point['value'];
            
            $playerCounts[] = [
                'timestamp' => $timestamp,
                'players' => $players
            ];
            
            $totalPlayers += $players;
            $maxPlayers = max($maxPlayers, $players);
            $minPlayers = min($minPlayers, $players);
            $dataPoints++;
        }
        
        $avgPlayers = $dataPoints > 0 ? round($totalPlayers / $dataPoints, 1) : 0;
        
        return [
            'average_players' => $avgPlayers,
            'peak_players' => $maxPlayers,
            'min_players' => $minPlayers === PHP_INT_MAX ? 0 : $minPlayers,
            'data_points' => $dataPoints,
            'player_counts' => $playerCounts,
            'timeframe' => '24h'
        ];
    }
    
    /**
     * Parse players data from BattleMetrics response
     */
    private function parsePlayersData($data) {
        if (!isset($data['data'])) {
            return null;
        }
        
        $players = [];
        
        foreach ($data['data'] as $player) {
            $players[] = [
                'id' => $player['id'],
                'name' => $player['attributes']['name'] ?? 'Unknown',
                'online' => true // If they're in the list, they're online
            ];
        }
        
        return [
            'count' => count($players),
            'players' => $players
        ];
    }
    
    /**
     * Cache management
     */
    private function getFromCache($key, $customDuration = null) {
        $file = $this->cache_dir . md5($key) . '.cache';
        
        if (!file_exists($file)) {
            return null;
        }
        
        $data = file_get_contents($file);
        $cached = unserialize($data);
        
        $duration = $customDuration ?? $this->config['integrations']['battlemetrics']['cache_duration'];
        
        if ($cached['expires'] < time()) {
            unlink($file);
            return null;
        }
        
        return $cached['data'];
    }
    
    private function saveToCache($key, $data, $customDuration = null) {
        $file = $this->cache_dir . md5($key) . '.cache';
        $duration = $customDuration ?? $this->config['integrations']['battlemetrics']['cache_duration'];
        
        $cached = [
            'data' => $data,
            'expires' => time() + $duration
        ];
        
        file_put_contents($file, serialize($cached));
    }
    
    private function ensureCacheDirectory() {
        if (!is_dir($this->cache_dir)) {
            mkdir($this->cache_dir, 0755, true);
        }
    }
    
    /**
     * Get enhanced server information combining BattleMetrics data
     */
    public function getEnhancedServerInfo($battlemetricsId, $serverData = []) {
        $bmData = $this->getServerInfo($battlemetricsId);
        $bmStats = $this->getServerStats($battlemetricsId);
        
        if (!$bmData) {
            return $serverData;
        }
        
        // Merge BattleMetrics data with existing server data
        $enhanced = array_merge($serverData, [
            'bm_name' => $bmData['name'],
            'bm_status' => $bmData['status'],
            'bm_players' => $bmData['players'],
            'bm_max_players' => $bmData['maxPlayers'],
            'bm_queue' => $bmData['queue'],
            'bm_map' => $bmData['map'],
            'bm_fps' => $bmData['fps'],
            'bm_entity_count' => $bmData['entityCount'],
            'bm_uptime' => $bmData['uptime'],
            'bm_last_wipe' => $bmData['lastWipe'],
            'bm_next_wipe' => $bmData['nextWipe'],
            'bm_rank' => $bmData['rank'],
            'bm_country' => $bmData['country'],
            'bm_modded' => $bmData['modded'],
            'bm_official' => $bmData['official'],
            'bm_pve' => $bmData['pve'],
            'bm_updated_at' => $bmData['updated_at']
        ]);
        
        if ($bmStats) {
            $enhanced = array_merge($enhanced, [
                'bm_avg_players' => $bmStats['average_players'],
                'bm_peak_players' => $bmStats['peak_players'],
                'bm_min_players' => $bmStats['min_players']
            ]);
        }
        
        return $enhanced;
    }
}

// Initialize BattleMetrics integration
$battleMetricsIntegration = new BattleMetricsIntegration($serverInfoTool->getConfig());
